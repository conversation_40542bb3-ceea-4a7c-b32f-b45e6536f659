package util

import (
	"image"
	"image/jpeg"
	"image/png"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
)

func TestGenerateImagesByURL(t *testing.T) {
	// 1. 准备多组测试图片文件
	pngFiles := []string{"test1.png", "test2.png"}
	jpgFiles := []string{"test1.jpg", "test2.jpg"}

	// 创建多个PNG测试文件
	for _, file := range pngFiles {
		pngImg := image.NewRGBA(image.Rect(0, 0, 100, 100))
		f, err := os.Create(file)
		if err != nil {
			t.Fatalf("创建测试PNG文件失败: %v", err)
		}
		defer os.Remove(file)
		defer f.Close()
		if err := png.Encode(f, pngImg); err != nil {
			t.Fatalf("写入PNG文件失败: %v", err)
		}
	}

	// 创建多个JPG测试文件
	for _, file := range jpgFiles {
		jpgImg := image.NewRGBA(image.Rect(0, 0, 100, 100))
		f, err := os.Create(file)
		if err != nil {
			t.Fatalf("创建测试JPG文件失败: %v", err)
		}
		defer os.Remove(file)
		defer f.Close()
		if err := jpeg.Encode(f, jpgImg, nil); err != nil {
			t.Fatalf("写入JPG文件失败: %v", err)
		}
	}

	// 2. 创建测试HTTP服务器返回多张图片
	ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case "/test1.png", "/test2.png":
			w.Header().Set("Content-Type", "image/png")
			_ = png.Encode(w, image.NewRGBA(image.Rect(0, 0, 100, 100)))
		case "/test1.jpg", "/test2.jpg":
			w.Header().Set("Content-Type", "image/jpeg")
			_ = jpeg.Encode(w, image.NewRGBA(image.Rect(0, 0, 100, 100)), nil)
		default:
			http.NotFound(w, r)
		}
	}))
	defer ts.Close()

	// 3. 测试用例 - 只保留纯PNG和纯JPG测试
	tests := []struct {
		name    string
		urls    []string
		wantErr bool
	}{
		{
			name:    "多个本地PNG文件",
			urls:    pngFiles,
			wantErr: false,
		},
		{
			name:    "多个本地JPG文件",
			urls:    jpgFiles,
			wantErr: false,
		},
		{
			name:    "多个HTTP PNG URL",
			urls:    []string{ts.URL + "/test1.png", ts.URL + "/test2.png"},
			wantErr: false,
		},
		{
			name:    "多个HTTP JPG URL",
			urls:    []string{ts.URL + "/test1.jpg", ts.URL + "/test2.jpg"},
			wantErr: false,
		},
	}

	// 4. 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := GenerateImagesByURL(tt.urls)
			if (err != nil) != tt.wantErr {
				t.Errorf("GenerateImagesByURL() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && len(got) != len(tt.urls) {
				t.Errorf("GenerateImagesByURL() 返回图片数量 = %d, 期望 %d", len(got), len(tt.urls))
			}
		})
	}
}
