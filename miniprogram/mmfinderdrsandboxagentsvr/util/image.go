// Package util 操作系统相关的工具方法
package util

import (
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/jpeg"
	"image/png"
	"net/http"
	"os"
	"path/filepath"
	"strings"
)

// ConcatImagesVertically 纵向地拼接图片
func ConcatImagesVertically(images []image.Image) image.Image {
	if len(images) == 0 {
		return nil
	}
	// 计算总高度和最大宽度
	totalHeight := 0
	maxWidth := 0
	for _, img := range images {
		bounds := img.Bounds()
		totalHeight += bounds.Dy()
		if bounds.Dx() > maxWidth {
			maxWidth = bounds.Dx()
		}
	}
	// 创建新画布（RGBA 支持透明度）
	res := image.NewRGBA(image.Rect(0, 0, maxWidth, totalHeight))
	draw.Draw(res, res.Bounds(), &image.Uniform{C: color.White}, image.Point{}, draw.Src)
	// 逐个绘制图像
	yOffset := 0
	for _, img := range images {
		bounds := img.Bounds()
		draw.Draw(
			res,
			image.Rect(0, yOffset, bounds.Dx(), yOffset+bounds.Dy()),
			img,
			bounds.Min,
			draw.Over,
		)
		yOffset += bounds.Dy()
	}
	return res
}

// GenerateImagesByURL 通过路径生成 image.Image 对象
func GenerateImagesByURL(cosURLs []string) ([]image.Image, error) {
	var imgList []image.Image
	for _, url := range cosURLs {
		var img image.Image
		var err error
		var resp *http.Response
		var file *os.File
		// 判断是否是 URL（包含 http:// 或 https://）
		if strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://") {
			// 处理 HTTP URL
			resp, err = http.Get(url)
			if err != nil {
				return nil, err
			}
			defer resp.Body.Close()
			// 根据 Content-Type 判断图片格式
			contentType := resp.Header.Get("Content-Type")
			if contentType == "image/png" {
				img, err = png.Decode(resp.Body)
			} else {
				img, err = jpeg.Decode(resp.Body)
			}
		} else {
			// 处理本地文件路径
			file, err = os.Open(url)
			if err != nil {
				return nil, err
			}
			defer file.Close()
			// 根据文件扩展名判断图像格式
			ext := strings.ToLower(filepath.Ext(url))
			if ext == ".png" {
				img, err = png.Decode(file)
			} else {
				img, err = jpeg.Decode(file)
			}
		}
		if err != nil {
			return nil, fmt.Errorf("解码图像失败，url=%s，error=%v", url, err)
		}
		imgList = append(imgList, img)
	}
	return imgList, nil
}
