// Package main
package main

import (
	"os"
	"os/signal"
	"syscall"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"

	"mmfinderdrsandboxagentsvr/api"
	_ "mmfinderdrsandboxagentsvr/env"
	_ "mmfinderdrsandboxagentsvr/middleware/db"
	_ "mmfinderdrsandboxagentsvr/middleware/wecube"
)

func main() {
	trpc.NewServer() // trpc
	api.Init()       // 初始化 API
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	<-sigCh
	log.Info("接收到 SIGTERM 系统信号，退出程序中")
	api.Exit()
	log.Info("退出程序完成")
}
