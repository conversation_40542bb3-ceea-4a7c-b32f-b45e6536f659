// Package cos 对象存储操作
package cos

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"image"
	"image/jpeg"
	"net/http"
	"net/url"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/tencentyun/cos-go-sdk-v5"
)

// UploadBytes 直接上传字节数据到 COS
func UploadBytes(ctx context.Context, data []byte) (url string, md5Hash string, err error) {
	// 计算数据的 MD5 哈希值
	hash := md5.Sum(data)
	md5Hash = hex.EncodeToString(hash[:])
	var httpReq *http.Request
	url = fmt.Sprintf("http://mirrors.tencent.com/repository/generic/kristendi/dify_tmp/%s", md5Hash)
	httpReq, err = http.NewRequest("PUT", url, bytes.NewReader(data))
	if err != nil {
		return
	}
	httpReq.SetBasicAuth("kristendi", "9be9b814db7c11eaadc96c92bf5e3645")
	httpReq.Header.Set("Content-Type", "application/octet-stream")
	var client http.Client
	httpResp, err := client.Do(httpReq)
	if err != nil {
		return
	}
	defer httpResp.Body.Close()
	log.WithContext(
		ctx,
		log.Field{Key: "url", Value: url},
	).Info("上传图片至 cos 结束")
	return
}

// UploadImage 上传图片至 cos
func UploadImage(ctx context.Context, img image.Image) (string, string, error) {
	var buffer bytes.Buffer
	if err := jpeg.Encode(&buffer, img, &jpeg.Options{Quality: 100}); err != nil {
		return "", "", err
	}
	return UploadBytes(ctx, buffer.Bytes())
}

// UploadImageToBucket 上传图片至 cos bucket
func UploadImageToBucket(img image.Image) (imgURL string, md5Hash string, err error) {
	var buffer bytes.Buffer
	err = jpeg.Encode(&buffer, img, &jpeg.Options{Quality: 100})
	if err != nil {
		return
	}
	hash := md5.Sum(buffer.Bytes())
	md5Hash = hex.EncodeToString(hash[:])

	// 使用 MD5 哈希值作为文件名
	objectKey := fmt.Sprintf("img/sandbox/%s.jpg", md5Hash)

	u, _ := url.Parse("http://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn")
	su, _ := url.Parse("http://cos-internal.ap-shanghai.tencentcos.cn")
	b := &cos.BaseURL{BucketURL: u, ServiceURL: su}
	c := cos.NewClient(b, &http.Client{
		Timeout: 100 * time.Second,
		Transport: &cos.AuthorizationTransport{
			SecretID:  "AKIDgBSWLyNYVFk15vWt64nGYfqy02sUr0vc",
			SecretKey: "VmpeEahaGhtvkKhqkPpMflcEb9SYQ3QZ",
		},
	})

	// 上传文件到 COS
	options := &cos.ObjectPutOptions{}
	options.ObjectPutHeaderOptions = &cos.ObjectPutHeaderOptions{
		ContentType: "image/jpg",
	}

	// 上传到bucket
	reader := bytes.NewReader(buffer.Bytes())
	_, err = c.Object.Put(context.Background(), objectKey, reader, options)

	if err != nil {
		return
	}

	// 拼接图片URL
	imageURL := fmt.Sprintf("http://aimusicdatasetcos-1258344707.cos-internal.ap-shanghai.tencentcos.cn/%s", objectKey)
	return imageURL, md5Hash, err
}
