// Package clickhouse 存储特征
package clickhouse

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"mmfinderdrsandboxagentsvr/env"
	"strconv"
	"time"

	mmdcfisherimportmsgpbgo "git.woa.com/brightfu/proto/mmdatagateway/proto/mmdcfisherimportmsg.pb.go"
	"github.com/apache/pulsar-client-go/pulsar"
	"google.golang.org/protobuf/proto"
)

// PulsarClientOption 定义Pulsar客户端的配置选项
type PulsarClientOption struct {
	ServiceURL          string
	AuthenticationToken string
}

// PulsarProducerOption 定义Pulsar生产者的配置选项
type PulsarProducerOption struct {
	Topic                     string
	BatchingMaxMessages       uint
	BatchingMaxPublishDelayMs int
}

// PulsarClient 表示一个Pulsar客户端
type PulsarClient struct {
	client     pulsar.Client                     // Pulsar客户端实例
	producer   pulsar.Producer                   // Pulsar生产者实例
	queue      chan *mmdcfisherimportmsgpbgo.Row // 消息队列
	batchSize  int                               // 批量大小
	flushDelay time.Duration                     // 刷新延迟时间
	stop       bool                              // 停止标志
}

var fisherImport *PulsarClient

// 一. init 初始化Pulsar客户端
func init() {
	clientOpts := PulsarClientOption{
		ServiceURL:          env.Conf.Pulsar.ServiceURL,
		AuthenticationToken: env.Conf.Pulsar.AuthenticationToken,
	}
	producerOpts := PulsarProducerOption{
		Topic:                     env.Conf.Pulsar.Topic,
		BatchingMaxMessages:       10,
		BatchingMaxPublishDelayMs: 1000,
	}
	var err error
	fisherImport, err = NewPulsarClient(clientOpts, producerOpts)
	if err != nil {
		log.Fatalf("Failed to initialize Pulsar client: %v", err)
	}
}

// NewPulsarClient 初始化一个新的Pulsar客户端和生产者
func NewPulsarClient(clientOpts PulsarClientOption, producerOpts PulsarProducerOption) (*PulsarClient, error) {
	client, err := pulsar.NewClient(pulsar.ClientOptions{
		URL:            clientOpts.ServiceURL,
		Authentication: pulsar.NewAuthenticationToken(clientOpts.AuthenticationToken),
	})
	if err != nil {
		return nil, fmt.Errorf("创建Pulsar客户端失败: %v", err)
	}

	producer, err := client.CreateProducer(pulsar.ProducerOptions{
		Topic:                   producerOpts.Topic,
		BatchingMaxPublishDelay: time.Duration(producerOpts.BatchingMaxPublishDelayMs) * time.Millisecond,
		BatchingMaxMessages:     producerOpts.BatchingMaxMessages,
	})
	if err != nil {
		client.Close()
		return nil, fmt.Errorf("创建Pulsar生产者失败: %v", err)
	}

	clientInstance := &PulsarClient{
		client:     client,
		producer:   producer,
		queue:      make(chan *mmdcfisherimportmsgpbgo.Row, producerOpts.BatchingMaxMessages),
		batchSize:  int(producerOpts.BatchingMaxMessages),
		flushDelay: time.Duration(producerOpts.BatchingMaxPublishDelayMs) * time.Millisecond,
		stop:       false,
	}

	// go clientInstance.flush()
	return clientInstance, nil
}

// AddRowProtobuf sends a protobuf message to Pulsar using the mmdcfisherimport format
func (p *PulsarClient) AddRowProtobuf(data map[string]interface{}) {
	// Create a Row message according to mmdcfisherimport format
	row := &mmdcfisherimportmsgpbgo.Row{
		Cells: make(map[string]*mmdcfisherimportmsgpbgo.Value),
	}

	for key, value := range data {
		cellValue := &mmdcfisherimportmsgpbgo.Value{
			Data: &mmdcfisherimportmsgpbgo.Data{},
		}

		switch v := value.(type) {
		case string:
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_STR
			cellValue.Data.StrVal = []byte(v)
		case []byte:
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_STR
			if isUTF8(v) {
				cellValue.Data.StrVal = v
			} else {
				cellValue.Data.StrVal = []byte(base64.StdEncoding.EncodeToString(v))
			}
		case int:
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_INT64
			cellValue.Data.Int64Val = int64(v)
		case int64:
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_INT64
			cellValue.Data.Int64Val = v
		case float64:
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_DOUBLE
			cellValue.Data.DoubleVal = v
		case []string:
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_STR_ARRAY
			var byteArray [][]byte
			for _, str := range v {
				byteArray = append(byteArray, []byte(str))
			}
			cellValue.Data.StrArrayVal = byteArray
		case []int64:
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_INT64_ARRAY
			cellValue.Data.Int64ArrayVal = v
		case []float64:
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_FLOAT_ARRAY
			cellValue.Data.DoubleArrayVal = v
		case map[string]interface{}:
			// For complex nested structures, convert to JSON string
			jsonData, _ := json.Marshal(v)
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_STR
			cellValue.Data.StrVal = jsonData
		default:
			continue
		}

		row.Cells[key] = cellValue
	}

	// Create a Blob to contain the Row
	blob := &mmdcfisherimportmsgpbgo.Blob{
		Rows: []*mmdcfisherimportmsgpbgo.Row{row},
	}

	// Serialize the protobuf message
	protoData, err := proto.Marshal(blob)
	if err != nil {
		log.Printf("Error serializing protobuf message: %v", err)
		return
	}

	// Send the serialized protobuf message to Pulsar
	p.producer.SendAsync(context.Background(), &pulsar.ProducerMessage{
		Payload: protoData,
	}, func(msgID pulsar.MessageID, message *pulsar.ProducerMessage, err error) {
		if err != nil {
			log.Printf("Failed to send message: %v", err)
		} else {
			log.Printf("Message sent to partition %d (Message ID: %v)", msgID.PartitionIdx(), msgID)
		}
	})
}

// AddRow 发送数据到Pulsar
func (p *PulsarClient) AddRow(data map[string]interface{}) {
	row := &mmdcfisherimportmsgpbgo.Row{
		Cells: make(map[string]*mmdcfisherimportmsgpbgo.Value),
	}
	for key, value := range data {
		cellValue := &mmdcfisherimportmsgpbgo.Value{
			Data: &mmdcfisherimportmsgpbgo.Data{},
		}
		switch v := value.(type) {
		case string:
			cellValue.Data.StrVal = []byte(v)
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_STR
		case []byte:
			if isUTF8(v) {
				cellValue.Data.StrVal = []byte(v)
			} else {
				cellValue.Data.StrVal = []byte(base64.StdEncoding.EncodeToString(v))
			}
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_STR
		case int:
			cellValue.Data.Int64Val = int64(v)
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_INT64
		case float64:
			cellValue.Data.DoubleVal = v
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_DOUBLE
		case []string:
			var byteArray [][]byte
			var int64Array []int64
			for _, str := range v {
				byteArray = append(byteArray, []byte(str))
				// 将字符串转换为int64，如果转换失败则使用0
				if num, err := strconv.ParseInt(str, 10, 64); err == nil {
					int64Array = append(int64Array, num)
				} else {
					int64Array = append(int64Array, 0)
				}
			}
			cellValue.Data.StrArrayVal = byteArray
			cellValue.Data.Int64ArrayVal = int64Array
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_INT64_ARRAY
		case []float64:
			cellValue.Data.DoubleArrayVal = v
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_FLOAT_ARRAY
		case map[string]interface{}:
			jsonData, _ := json.Marshal(v) // jsonData为一个字节切片
			cellValue.Data.StrVal = jsonData
			cellValue.DataType = mmdcfisherimportmsgpbgo.DataType_DATA_TYPE_STR
		default:
			continue
		}
		row.Cells[key] = cellValue
	}
	// 直接发送单条消息并添加回调
	jsonData, err := json.Marshal([]*mmdcfisherimportmsgpbgo.Row{row})
	if err != nil {
		log.Printf("序列化数据错误: %v", err)
		return
	}
	p.producer.SendAsync(context.Background(), &pulsar.ProducerMessage{
		Payload: jsonData,
	}, func(msgID pulsar.MessageID, message *pulsar.ProducerMessage, err error) {
		if err != nil {
			log.Printf("发送消息失败: %v", err)
		} else {
			log.Printf("消息已发送到分区 %d (消息ID: %v)", msgID.PartitionIdx(), msgID)
		}
	})
}

// flush 批量处理并发送消息到Pulsar服务器
func (p *PulsarClient) flush() {
	sendCallback := func(msgID pulsar.MessageID, msg *pulsar.ProducerMessage, err error) {
		if err != nil {
			fmt.Println("发送消息失败:", err)
		}
	}
	for !p.stop {
		var collectedRows []*mmdcfisherimportmsgpbgo.Row

		for len(collectedRows) < p.batchSize {
			select {
			case row := <-p.queue:
				collectedRows = append(collectedRows, row)
			case <-time.After(p.flushDelay):
				if len(collectedRows) > 0 {
					break
				}
			}
		}

		if len(collectedRows) > 0 {
			jsonData, err := json.Marshal(collectedRows)
			if err != nil {
				fmt.Println("序列化数据错误:", err)
				continue
			}

			msg := &pulsar.ProducerMessage{
				Payload: jsonData,
			}
			p.producer.SendAsync(context.Background(), msg, func(msgID pulsar.MessageID, message *pulsar.ProducerMessage, err error) {
				if err == nil {
					log.Printf("消息已发送到分区 %d", msgID.PartitionIdx())
				}
				if err != nil {
					log.Printf("发送消息失败: %v", err)
				}
				sendCallback(msgID, message, err)
			})
		}
	}
}

// Close 释放Pulsar资源
func (p *PulsarClient) Close() {
	p.stop = true
	if p.producer != nil {
		p.producer.Close()
	}
	if p.client != nil {
		p.client.Close()
	}
}

// isUTF8 检查是否为UTF-8编码数据
func isUTF8(s []byte) bool {
	return json.Valid(s) // 简单的UTF-8编码数据检查
}

// SendReportProtobuf sends a report using the protobuf format
func SendReportProtobuf(req string, resp string, note string) {
	data := map[string]interface{}{
		"timestamp": time.Now().Unix(),
		"req":       req,
		"resp":      resp,
		"note":      note,
	}

	fisherImport.AddRowProtobuf(data)

	// Wait for 10 seconds before closing
	time.Sleep(10 * time.Second)
	if fisherImport != nil {
		fisherImport.producer.FlushWithCtx(context.Background())
		fisherImport.Close()
	}
	log.Println("All messages processed, connection closed")
}
