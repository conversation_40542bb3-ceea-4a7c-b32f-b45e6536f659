// Package agent 定义相关的接口类型
package agent

// PositionInfo 定义相关的接口类型
type PositionInfo struct {
	Latitude  float64 `json:"latitude"`  // 纬度，默认值 -1 表示无效值
	Longitude float64 `json:"longitude"` // 经度，默认指 -1 表示无效值
}

// BaseReq 定义相关的接口类型
type BaseReq struct {
	AppID            string `json:"app_id"`
	AppName          string `json:"app_name"`
	BaseURL          string `json:"base_url"`
	Instruction      string `json:"instruction"`
	ModelName        string `json:"model_name"`
	Prompt           string `json:"prompt"`
	PromptVLT        string `json:"prompt_vlt"`
	PromptVLTV1      string `json:"prompt_vlt_v1"`
	RunMode          string `json:"run_mode"` // 脚本的运行模式
	UseVLT           string `json:"use_vlt"`
	UseWaitModel     string `json:"use_wait_model"`
	VLTBaseURL       string `json:"vlt_base_url"`
	VLTBaseURLV1     string `json:"vlt_base_url_v1"`
	VLTModelName     string `json:"vlt_model_name"`
	VLTModelNameV1   string `json:"vlt_model_name_v1"`
	WaitModelBaseURL string `json:"wait_model_base_url"`
	WaitModelName    string `json:"wait_model_name"`
}

// Args 获取执行脚本参数与环境变量
func (req *BaseReq) Args() ([]string, []string) {
	args := []string{
		"--app_id", req.AppID,
	}
	if req.AppName != "" {
		args = append(args, "--app_name")
		args = append(args, req.AppName)
	}
	if req.BaseURL != "" {
		args = append(args, "--base_url")
		args = append(args, req.BaseURL)
	}
	if req.Instruction != "" {
		args = append(args, "--instruction")
		args = append(args, req.Instruction)
	}
	if req.ModelName != "" {
		args = append(args, "--model_name")
		args = append(args, req.ModelName)
	}
	if req.Prompt != "" {
		args = append(args, "--prompt")
		args = append(args, req.Prompt)
	}
	if req.PromptVLT != "" {
		args = append(args, "--prompt_vlt")
		args = append(args, req.PromptVLT)
	}
	if req.PromptVLTV1 != "" {
		args = append(args, "--prompt_vlt_v1")
		args = append(args, req.PromptVLTV1)
	}
	if req.RunMode != "" {
		args = append(args, "--run_mode")
		args = append(args, req.RunMode)
	}
	if req.UseVLT != "" {
		args = append(args, "--use_vlt")
		args = append(args, req.UseVLT)
	}
	if req.UseWaitModel != "" {
		args = append(args, "--use_wait_model")
		args = append(args, req.UseWaitModel)
	}
	if req.VLTBaseURL != "" {
		args = append(args, "--vlt_base_url")
		args = append(args, req.VLTBaseURL)
	}
	if req.VLTBaseURLV1 != "" {
		args = append(args, "--vlt_base_url_v1")
		args = append(args, req.VLTBaseURLV1)
	}
	if req.VLTModelName != "" {
		args = append(args, "--vlt_model_name")
		args = append(args, req.VLTModelName)
	}
	if req.VLTModelNameV1 != "" {
		args = append(args, "--vlt_model_name_v1")
		args = append(args, req.VLTModelNameV1)
	}
	if req.WaitModelBaseURL != "" {
		args = append(args, "--wait_model_base_url")
		args = append(args, req.WaitModelBaseURL)
	}
	if req.WaitModelName != "" {
		args = append(args, "--wait_model_name")
		args = append(args, req.WaitModelName)
	}
	return args, []string{}
}

// RunReq 定义相关的接口类型
type RunReq struct {
	BaseReq
	AuthCode         string        `json:"auth_code"`
	Position         *PositionInfo `json:"position"`
	SkipShareURLData bool          `json:"skip_share_url_data"`
}

// RunHeadlessReq 无头请求
type RunHeadlessReq struct {
	BaseReq
	FromUsername string `json:"from_username"`
	HeadlessMode string `json:"headless_mode"`
	RequestID    string `json:"request_id"`
	UIN          string `json:"uin"`
	Username     string `json:"username"`
}

// Args 获取执行脚本参数与环境变量
func (req *RunHeadlessReq) Args() ([]string, []string) {
	args, envs := req.BaseReq.Args()
	if req.FromUsername != "" {
		envs = append(envs, "WXMS_FROM_USERNAME="+req.FromUsername)
	}
	if req.HeadlessMode != "" {
		envs = append(envs, "WXMS_HEADLESS_MODE="+req.HeadlessMode)
	}
	if req.RequestID != "" {
		envs = append(envs, "WXMS_REQUEST_ID="+req.RequestID)
	}
	if req.UIN != "" {
		envs = append(envs, "WXMS_UIN="+req.UIN)
	}
	if req.Username != "" {
		envs = append(envs, "WXMS_USERNAME="+req.Username)
	}
	return args, envs
}

// BaseRespData 定义相关的接口类型
type BaseRespData struct {
	DebugStr          string   `json:"debug_str"`
	LongImgURL        string   `json:"long_img_url"`
	LongUniqueImgURL  string   `json:"long_unique_img_url"`
	Ret               int      `json:"ret"`
	ScreenshotImgURLs []string `json:"screenshot_img_urls"`
	ShareURL          string   `json:"share_url"`
	Thinking          []string `json:"thinking"`
}
