// Package agent 定义相关的 service 类型
package agent

import (
	"encoding/json"
)

// PythonResult 推理脚本的返回
type PythonResult struct {
	Answers        []string                   `json:"answers"`
	AnswersRawData []string                   `json:"answers_raw_data"`
	Interrupt      map[string]json.RawMessage `json:"interrupt"`
	LabelIndexs    []string                   `json:"label_indexs"`
	Screens        []string                   `json:"screens"`
	Screenshots    []string                   `json:"screenshots"`
	Status         string                     `json:"status"`
	TargetID       string                     `json:"target_id"`
	Xmls           []string                   `json:"xmls"`
	Xpaths         []string                   `json:"xpaths"`
}

// PythonResultDTO 推理脚本返回的二次封装
type PythonResultDTO struct {
	PythonResult *PythonResult `json:"python_result"`
	SharedData   string        `json:"shared_data"`
}

// GetPythonResult 获取 PythonResult
func (dto *PythonResultDTO) GetPythonResult() *PythonResult {
	if dto == nil || dto.PythonResult == nil {
		return &PythonResult{Interrupt: make(map[string]json.RawMessage)}
	}
	return dto.PythonResult
}
