package agent

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestFindAvailableJSON(t *testing.T) {
	cases := []struct {
		input    string
		prefix   []byte
		reverse  bool
		expected string
	}{
		{
			"\n\n{\"code\": -1, \"msg\": \"用户中断\"}\nTraceback\n  File\n    res =\n          ^\n\n",
			nil, true,
			"{\"code\": -1, \"msg\": \"用户中断\"}",
		},
		{
			"\n{\"code\":0,\"msg\":\"abc\"}\nbody\001{\"code\": -1, \"msg\": \"用户中断\"}\nTraceback\n  File\n  res =\n ^\n\n",
			[]byte("body\001"), true,
			"{\"code\": -1, \"msg\": \"用户中断\"}",
		},
		{
			"\n\"msg\":\"abc\"}\n{\"code\": -1, \"msg\nTraceback\n  File\n  res =\n ^\n\n",
			nil, true,
			"",
		},
		{
			"\n{\"code\":0,\"msg\":\"abc\"}\n{\"code\": -1, \"msg\": \"用户中断\"}\nTraceback\n  File\n  res =\n ^\n\n",
			nil, false,
			"{\"code\":0,\"msg\":\"abc\"}",
		},
		{
			"\n{\"code\":0,\"msg\":\"abc\"}\n{\"code\": -1, \"msg\": \"用户中断\"}\nTraceback\n  File\n  res =\n ^\n\n",
			nil, true,
			"{\"code\": -1, \"msg\": \"用户中断\"}",
		},
	}

	for i, tc := range cases {
		result, err := findAvailableJSON([]byte(tc.input), tc.prefix, tc.reverse)
		if tc.expected == "" {
			require.Error(t, err, "case %d", i)
			continue
		}
		require.NoError(t, err, "case %d", i)
		require.Equal(t, tc.expected, string(result), "case %d", i)
	}
}

func TestProcessScreenshots(t *testing.T) {
	// 模拟COS图片URL
	cosURLs := []string{
		"/home/<USER>/workspace/1.jpeg",
	}

	// 3. 调用被测函数
	longHash, uniqueURL, screenshotURLs, errCode, err := processScreenshots(context.Background(), cosURLs)
	if err != nil {
		t.Fatalf("processScreenshots 失败: errCode=%d, err=%v", errCode, err)
	}

	// 4. 验证结果
	fmt.Println("测试结果:")
	fmt.Printf("长图哈希: %s\n", longHash)
	fmt.Printf("去重长图URL: %s\n", uniqueURL)
	fmt.Printf("截图URL列表: %v\n", screenshotURLs)

	// 验证返回的URL数量
	if len(screenshotURLs) != 3 {
		t.Errorf("期望返回3个截图URL, 实际返回%d个", len(screenshotURLs))
	}

	// 验证长图哈希不为空
	if longHash == "" {
		t.Error("长图哈希不应为空")
	}

	// 验证去重长图URL不为空
	if uniqueURL == "" {
		t.Error("去重长图URL不应为空")
	}
}
