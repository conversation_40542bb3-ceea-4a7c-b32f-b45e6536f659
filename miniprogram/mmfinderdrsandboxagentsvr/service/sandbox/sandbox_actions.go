// Package sandbox 沙箱在线操作实现
package sandbox

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	_ "image/jpeg" // 注册JPEG解码器
	"math/rand"
	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"
	"mmfinderdrsandboxagentsvr/util"
	"os"
	"path/filepath"
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

// ScreenshotAction 截图操作
func ScreenshotAction(ctx context.Context, targetID string) (string, error) {
	// 构建CDP请求
	taskID := util.RandomID(32)
	req := &sandbox_api_model.CDPProxyReq{
		TargetID: &targetID,
		Method:   "Page.captureScreenshot",
		Params: map[string]interface{}{
			"task_id": taskID,
		},
	}

	// 执行指令
	resp, err := SandboxOnlineObj.ExecuteInstruction(ctx, req)
	if err != nil {
		log.Errorf("截图失败: %v", err)
		return "", err
	}

	// 解析响应
	if resp.Data == nil || resp.Data.Result == nil {
		return "", fmt.Errorf("截图响应数据为空")
	}

	// 获取截图数据
	resultData, ok := resp.Data.Result["data"]
	if !ok {
		return "", fmt.Errorf("截图数据不存在")
	}

	// 解码Base64图片数据
	imageBase64, ok := resultData.(string)
	if !ok {
		return "", fmt.Errorf("截图数据格式错误")
	}

	// 保存图片到临时文件
	imageBytes, err := base64.StdEncoding.DecodeString(imageBase64)
	if err != nil {
		return "", fmt.Errorf("解码截图数据失败: %v", err)
	}

	// 创建临时目录
	tempDir := os.TempDir()
	timestamp := time.Now().Format("20060102150405")
	filename := fmt.Sprintf("screenshot_%s_%s.jpg", targetID, timestamp)
	imagePath := filepath.Join(tempDir, filename)

	// 写入文件
	if writeErr := os.WriteFile(imagePath, imageBytes, 0644); writeErr != nil {
		return "", fmt.Errorf("保存截图失败: %v", writeErr)
	}

	return imagePath, nil
}

// executeTouchEvent 执行触摸事件
func executeTouchEvent(
	ctx context.Context,
	targetID string,
	eventType string,
	touchPoints []map[string]interface{},
) error {
	taskID := util.RandomID(32)
	params := map[string]interface{}{
		"task_id":     taskID,
		"type":        eventType,
		"touchPoints": touchPoints,
	}

	req := &sandbox_api_model.CDPProxyReq{
		TargetID: &targetID,
		Method:   "Input.dispatchTouchEvent",
		Params:   params,
	}

	_, err := SandboxOnlineObj.ExecuteInstruction(ctx, req)
	if err != nil {
		log.Errorf("触摸%s事件失败: %v", eventType, err)
		return err
	}

	return nil
}

// ClickAction 点击操作
func ClickAction(ctx context.Context, targetID string, x, y float64) error {
	// 构建触摸事件ID
	touchEventID := time.Now().UnixNano()

	// 构建触摸点
	touchPoint := map[string]interface{}{
		"x":  x,
		"y":  y,
		"id": touchEventID,
	}

	// 执行触摸开始事件
	if err := executeTouchEvent(ctx, targetID, "touchStart", []map[string]interface{}{touchPoint}); err != nil {
		return err
	}

	// 执行触摸结束事件
	if err := executeTouchEvent(ctx, targetID, "touchEnd", []map[string]interface{}{touchPoint}); err != nil {
		return err
	}

	return nil
}

// ScrollAction 滚动操作
func ScrollAction(
	ctx context.Context,
	targetID string, x, y float64, deltaX, deltaY float64,
) error {
	// 构建触摸事件ID
	touchEventID := time.Now().UnixNano()

	// 触摸开始点
	startPoint := map[string]interface{}{
		"x":  x,
		"y":  y,
		"id": touchEventID,
	}

	// 触摸结束点
	endPoint := map[string]interface{}{
		"x":  x + deltaX,
		"y":  y + deltaY,
		"id": touchEventID,
	}

	// 执行触摸开始事件
	if err := executeTouchEvent(ctx, targetID, "touchStart", []map[string]interface{}{startPoint}); err != nil {
		return err
	}

	// 执行触摸移动事件
	if err := executeTouchEvent(ctx, targetID, "touchMove", []map[string]interface{}{endPoint}); err != nil {
		return err
	}

	// 执行触摸结束事件
	if err := executeTouchEvent(ctx, targetID, "touchEnd", []map[string]interface{}{endPoint}); err != nil {
		return err
	}

	return nil
}

// EnableDomXML 启用DOM XML
func EnableDomXML(ctx context.Context, targetID string) error {
	// 构建CDP请求
	req := &sandbox_api_model.CDPProxyReq{
		TargetID: &targetID,
		Method:   "DOM.enable",
		Params:   map[string]interface{}{},
	}

	// 执行指令
	_, err := SandboxOnlineObj.ExecuteInstruction(ctx, req)
	if err != nil {
		log.Errorf("启用DOM XML失败: %v", err)
		return err
	}

	return nil
}

// InputInsertText 输入文本
func InputInsertText(ctx context.Context, targetID string, text string) error {
	// 构建CDP请求
	req := &sandbox_api_model.CDPProxyReq{
		TargetID: &targetID,
		Method:   "Input.insertText",
		Params: map[string]interface{}{
			"text": text,
		},
	}

	// 执行指令
	_, err := SandboxOnlineObj.ExecuteInstruction(ctx, req)
	if err != nil {
		log.Errorf("插入文本失败: %v", err)
		return err
	}

	return nil
}

// GetDomXML 获取DOM XML
func GetDomXML(ctx context.Context, targetID string) (string, error) {
	// 读取js_code_xbar.js的内容
	jsCode, err := os.ReadFile("/home/<USER>/mmfinderdrsandboxagentsvr/pyfile/sdk/wxms/js_code_xbar.js")
	if err != nil {
		log.Errorf("读取js_code_xbar.js失败: %v", err)
		return "", err
	}

	// 构建CDP请求
	req := &sandbox_api_model.CDPProxyReq{
		TargetID: &targetID,
		Method:   "Runtime.evaluate",
		Params: map[string]interface{}{
			"expression":    string(jsCode) + GetDomXMLJsExpression,
			"returnByValue": true,
		},
	}

	// 执行指令
	resp, err := SandboxOnlineObj.ExecuteInstruction(ctx, req)
	if err != nil {
		log.Errorf("获取DOM XML失败: %v", err)
		return "", err
	}

	// 解析响应
	if resp.Data == nil || resp.Data.Result == nil {
		return "", fmt.Errorf("DOM XML响应数据为空")
	}

	// 尝试从结果中提取DOM XML
	result, ok := resp.Data.Result["result"]
	if !ok {
		return "", fmt.Errorf("DOM XML结果不存在")
	}

	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return "", fmt.Errorf("DOM XML结果格式错误")
	}

	value, ok := resultMap["value"]
	if !ok {
		return "", fmt.Errorf("DOM XML值不存在")
	}

	domXML, ok := value.(string)
	if !ok {
		return "", fmt.Errorf("DOM XML值格式错误")
	}

	return domXML, nil
}

// GetAllElementsRects 获取所有元素矩形
func GetAllElementsRects(ctx context.Context, targetID string) (string, error) {
	// 读取js_code_xbar.js的内容
	jsCode, err := os.ReadFile("/home/<USER>/mmfinderdrsandboxagentsvr/pyfile/sdk/wxms/js_code_xbar.js")
	if err != nil {
		log.Errorf("读取js_code_xbar.js失败: %v", err)
		return "", err
	}

	allElementsRects := make([]interface{}, 0)
	// 构建CDP请求去获取非原生组件的矩形
	nonNativeExpression := string(jsCode) + GetAllElementsRectsJsExpression
	nonNativeElementsRects, shouldReturn, err := getElementsRects(ctx, targetID, nonNativeExpression)
	if shouldReturn {
		return "", err
	}

	// 构建CDP请求去获取原生组件的矩形
	nativeExpression := string(jsCode) + GetAllNativeElementsRectsJsExpression
	nativeElementsRects, shouldReturn, err := getElementsRects(ctx, targetID, nativeExpression)
	if shouldReturn {
		return "", err
	}

	// 合并非原生组件和原生组件的矩形
	allElementsRects = append(allElementsRects, nonNativeElementsRects...)
	allElementsRects = append(allElementsRects, nativeElementsRects...)

	// json序列化
	jsonData, err := json.Marshal(allElementsRects)
	if err != nil {
		return "", fmt.Errorf("序列化所有元素矩形失败: %v", err)
	}

	return string(jsonData), nil
}

func getElementsRects(ctx context.Context, targetID string, expression string) ([]interface{}, bool, error) {
	req := &sandbox_api_model.CDPProxyReq{
		TargetID: &targetID,
		Method:   "Runtime.evaluate",
		Params: map[string]interface{}{
			"expression":    expression,
			"returnByValue": true,
		},
	}

	// 执行指令
	resp, err := SandboxOnlineObj.ExecuteInstruction(ctx, req)
	if err != nil {
		log.Errorf("获取所有元素矩形失败: %v", err)
		return nil, true, err
	}

	// 解析响应
	if resp.Data == nil || resp.Data.Result == nil {
		return nil, true, fmt.Errorf("所有元素矩形响应数据为空")
	}

	// 尝试从结果中提取DOM XML
	result, ok := resp.Data.Result["result"]
	if !ok {
		return nil, true, fmt.Errorf("所有元素矩形结果不存在")
	}

	resultMap, ok := result.(map[string]interface{})
	if !ok {
		return nil, true, fmt.Errorf("所有元素矩形结果格式错误")
	}

	value, ok := resultMap["value"]
	if !ok {
		return nil, true, fmt.Errorf("所有元素矩形值不存在")
	}

	allElementsRects, ok := value.([]interface{})
	if !ok {
		return nil, true, fmt.Errorf("所有元素矩形值格式错误")
	}
	return allElementsRects, false, nil
}

// LaunchAppletAction 唤起小程序操作
func LaunchAppletAction(ctx context.Context, appID string) error {
	// 构建CDP请求
	taskID := strconv.Itoa(rand.Intn(10000000))
	req := &sandbox_api_model.CDPProxyReq{
		Method: "XWeb.LaunchApplet",
		Params: map[string]interface{}{
			"role":      "server",
			"task_id":   taskID,
			"app_id":    appID,
			"entry_url": "",
		},
	}

	// 执行指令
	_, err := SandboxOnlineObj.ExecuteInstruction(ctx, req)
	if err != nil {
		log.Errorf("唤起小程序失败: %v", err)
		return err
	}

	return nil
}

// CloseAppletAction 关闭小程序操作
func CloseAppletAction(ctx context.Context, appID string) error {
	// 构建CDP请求
	taskID := strconv.Itoa(rand.Intn(10000000))
	req := &sandbox_api_model.CDPProxyReq{
		Method: "XWeb.CloseApplet",
		Params: map[string]interface{}{
			"role":    "server",
			"task_id": taskID,
			"app_id":  appID,
		},
	}

	// 执行指令
	_, err := SandboxOnlineObj.ExecuteInstruction(ctx, req)
	if err != nil {
		log.Errorf("关闭小程序失败: %v", err)
		return err
	}

	return nil
}

// GetTargetsAction 获取当前所有目标（小程序）列表
func GetTargetsAction(ctx context.Context) ([]map[string]interface{}, error) {
	// 构建CDP请求
	req := &sandbox_api_model.CDPProxyReq{
		Method: "XWeb.targets",
		Params: map[string]interface{}{
			"role": "server",
		},
	}

	// 执行指令
	resp, err := SandboxOnlineObj.ExecuteInstruction(ctx, req)
	if err != nil {
		log.Errorf("获取目标列表失败: %v", err)
		return nil, err
	}

	// 解析响应
	if resp.Data == nil || resp.Data.Data == nil {
		return nil, fmt.Errorf("获取目标列表响应数据为空")
	}

	// 尝试从结果中提取targets
	var targetsData []map[string]interface{}

	// 根据日志分析，resp.Data.Data 已经是一个切片类型
	// 尝试不同的类型断言方式
	switch data := resp.Data.Data.(type) {
	case []map[string]interface{}:
		// 直接是目标类型
		targetsData = data
	case []interface{}:
		// 是一个通用接口切片，需要转换每个元素
		targetsData = make([]map[string]interface{}, len(data))
		for i, item := range data {
			if mapItem, ok := item.(map[string]interface{}); ok {
				targetsData[i] = mapItem
			} else {
				return nil, fmt.Errorf("获取目标列表响应数据格式错误：元素 %d 不是 map 类型", i)
			}
		}
	default:
		return nil, fmt.Errorf("获取目标列表响应数据格式错误：%T", resp.Data.Data)
	}

	return targetsData, nil
}

// SetLocationAction 设置地理位置
func SetLocationAction(
	ctx context.Context, latitude, longitude float64,
) (*sandbox_api_model.ActionRespData, error) {
	// 执行设置地理位置操作
	resp, err := SandboxOnlineObj.SetLocation(ctx, latitude, longitude)
	if err != nil {
		log.Errorf("设置地理位置失败: %v", err)
		return nil, err
	}

	// 检查设置结果
	if resp.Code != 0 {
		return nil, fmt.Errorf("设置地理位置失败，错误码: %d, 错误信息: %s", resp.Code, resp.Message)
	}

	// 由于设置地理位置操作没有截图，我们无法获取实际的屏幕尺寸
	// 这里使用一个合理的默认值，或者可以考虑从配置中读取
	defaultWidth := 410
	defaultHeight := 776

	// 返回设置成功的响应
	return &sandbox_api_model.ActionRespData{
		URL:          "", // 设置地理位置操作没有截图
		DomXML:       fmt.Sprintf("<location latitude=\"%f\" longitude=\"%f\">success</location>", latitude, longitude),
		ScreenWidth:  defaultWidth,
		ScreenHeight: defaultHeight,
	}, nil
}

// SendKeyEventAction 发送键盘事件
func SendKeyEventAction(ctx context.Context, targetID string, param map[string]interface{}) error {
	// 构建CDP请求
	req := &sandbox_api_model.CDPProxyReq{
		TargetID: &targetID,
		Method:   "Input.dispatchKeyEvent",
		Params:   param,
	}

	// 执行指令
	_, err := SandboxOnlineObj.ExecuteInstruction(ctx, req)
	if err != nil {
		log.Errorf("发送键盘事件失败: %v", err)
		return err
	}

	return nil
}

// PressEnterAction 模拟按下并释放回车键操作
func PressEnterAction(ctx context.Context, targetID string) error {
	log.Infof("开始模拟按下并释放回车键")

	// 定义按下回车键的参数
	downParam := map[string]interface{}{
		"type":                  "keyDown",
		"key":                   "Enter",
		"windowsVirtualKeyCode": 0x0D,
		"code":                  "Enter",
		"unmodifiedText":        "\n",
		"text":                  "\n",
	}

	// 定义释放回车键的参数
	upParam := map[string]interface{}{
		"type":                  "keyUp",
		"key":                   "Enter",
		"windowsVirtualKeyCode": 0x0D,
		"code":                  "Enter",
		"unmodifiedText":        "\n",
		"text":                  "\n",
	}

	// 发送按下回车键的请求
	if err := SendKeyEventAction(ctx, targetID, downParam); err != nil {
		log.Errorf("模拟按下回车键失败: %v", err)
		return err
	}
	log.Infof("按下回车键事件已发送")

	// 发送释放回车键的请求
	if err := SendKeyEventAction(ctx, targetID, upParam); err != nil {
		log.Errorf("模拟释放回车键失败: %v", err)
		return err
	}
	log.Infof("释放回车键事件已发送")

	return nil
}
