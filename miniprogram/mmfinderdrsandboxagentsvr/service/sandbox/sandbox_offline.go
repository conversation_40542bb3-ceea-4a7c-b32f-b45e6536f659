// Package sandbox 离线沙箱
package sandbox

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mmfinderdrsandboxagentsvr/env"
	"mmfinderdrsandboxagentsvr/middleware/db"
	"mmfinderdrsandboxagentsvr/model/api/base"
	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"
	sandbox_service_model "mmfinderdrsandboxagentsvr/model/service/sandbox"
	"net"
	"net/http"
	"os"
	"os/exec"
	"sync/atomic"
	"syscall"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"git.woa.com/polaris/polaris-go/v2/api"
)

// Offline 离线沙箱的实体类
type Offline struct {
	HeartbeatStopFlag bool
	WechatProxy       *WechatProxy
	UserID            int64
	UserName          string
	UserPassword      string
	javaProxyCMD      *exec.Cmd
	wechatCMD         *exec.Cmd
}

var consumerAPI api.ConsumerAPI

// var SandboxOfflineObj *Offline

// InitSandboxOffline 初始化
func InitSandboxOffline() {
	cfg := api.NewConfiguration()
	cfg.GetGlobal().GetServerConnector().SetMessageTimeout(5 * time.Second)
	cfg.GetGlobal().GetServerConnector().SetMessageTimeout(5 * time.Second)
	consumerAPI, _ = api.NewConsumerAPIByConfig(cfg)
	// SandboxOfflineObj = NewSandboxOffline()
}

func getURL(path string) (url string, err error) {
	var flowID uint64
	getOneInstanceReq := &api.GetOneInstanceRequest{}
	getOneInstanceReq.FlowID = atomic.AddUint64(&flowID, 1)
	getOneInstanceReq.Namespace = "Production"
	getOneInstanceReq.Service = "mmlistenaudiofdsvr"
	getOneInstanceReq.LbPolicy = api.LBPolicyWeightedRandom
	// 获取服务实例
	getInstResp, err := consumerAPI.GetOneInstance(getOneInstanceReq)
	if err != nil {
		log.With(log.Field{Key: "error", Value: err}).Error("getURL 失败")
		return
	}
	if len(getInstResp.Instances) == 0 {
		err = fmt.Errorf("no available instances")
		log.With(log.Field{Key: "error", Value: err}).Error("getURL 失败")
		return
	}
	// 获取目标实例的 IP 和端口
	targetInstance := getInstResp.Instances[0]
	url = fmt.Sprintf("http://%s:%d%s", targetInstance.GetHost(), targetInstance.GetPort(), path)
	return
}

func requestTestAccount() (resp *sandbox_service_model.WXTestAccountGetUserResp, err error) {
	url, err := getURL("/wx_test_account/user")
	if err != nil {
		return
	}
	httpResp, err := http.Get(url)
	if err != nil {
		log.With(
			log.Field{Key: "url", Value: url},
			log.Field{Key: "error", Value: err},
		).Error("http.Get 失败")
		return
	}
	defer httpResp.Body.Close()
	if httpResp.StatusCode != http.StatusOK {
		err = fmt.Errorf("httpStatus=%d", httpResp.StatusCode)
		log.With(
			log.Field{Key: "url", Value: url},
			log.Field{Key: "error", Value: err},
		).Error("http.Get 失败")
		return
	}
	data, err := io.ReadAll(httpResp.Body)
	if err != nil {
		log.With(
			log.Field{Key: "url", Value: url},
			log.Field{Key: "error", Value: err},
		).Error("http.Get 失败")
		return
	}
	resp = &sandbox_service_model.WXTestAccountGetUserResp{}
	if err = json.Unmarshal(data, resp); err != nil {
		log.With(
			log.Field{Key: "url", Value: url},
			log.Field{Key: "error", Value: err},
		).Error("http.Get 失败")
		return
	}
	log.With(
		log.Field{Key: "id", Value: resp.Data.ID},
		log.Field{Key: "username", Value: resp.Data.Username},
		log.Field{Key: "password", Value: resp.Data.Password},
	).Info("向中心服务请求空闲测试账号成功")
	return
}

// NewSandboxOffline 创建一个单例的实体类对象
func NewSandboxOffline() (s *Offline) {
	s = &Offline{}
	// 1. 向中心服务请求空闲测试账号
	s.initTestAccount()
	log.Info("获取空闲测试账号成功")
	// 1. 启动 java proxy
	s.startJavaProxyAndEnsureStarted()
	log.Info("启动 java proxy 成功")
	// 2. 启动微信
	s.startWechatAndEnsureStarted()
	log.Info("启动微信成功")
	// 3. 启动微信链接
	s.WechatProxy = NewWechatProxy()
	log.Info("启动微信链接成功")
	// 4. 启动心跳监控
	s.startHeartbeat()
	log.Info("启动心跳监控成功")
	return
}

// ExecuteInstruction 操作微信
func (s *Offline) ExecuteInstruction(
	ctx context.Context,
	req *sandbox_api_model.CDPProxyReq,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	resp, err = s.WechatProxy.ExecuteInstruction(ctx, req)
	if err != nil {
		return
	}
	err = s.UpdateCliHeartbeated()
	return
}

// Exit 优雅退出程序
func (s *Offline) Exit() {
	// 先停止心跳后停止进程
	s.stopHeartbeat()
	if s.javaProxyCMD != nil {
		_ = s.javaProxyCMD.Process.Kill()
		_ = s.javaProxyCMD.Wait()
		s.javaProxyCMD = nil
	}
	if s.wechatCMD != nil {
		_ = s.wechatCMD.Process.Kill()
		_ = s.wechatCMD.Wait()
		s.wechatCMD = nil
	}
	s.WechatProxy.Exit()
}

// UpdateCliHeartbeated 更新客户端信息的心跳
func (s *Offline) UpdateCliHeartbeated() error {
	now := time.Now()
	return db.UpdateAIMiniProgramSandbox(s.UserName, nil, &now)
}

func (s *Offline) heartbeat() {
	// 1. 微信探活
	ok := false
	for range 3 {
		if !ok {
			_, err := s.WechatProxy.SetWechatClient()
			ok = err == nil
		}
	}
	if !ok {
		log.Error("微信探活失败，退出 agent 程序")
		s.Exit()
		return
	}
	// 2. 更新数据库
	if err := db.InsertAndUpdateAIMiniProgramSandbox(s.UserID, s.UserName, s.UserPassword, env.IP); err != nil {
		log.With(log.Field{Key: "error", Value: err}).Error("数据库心跳记录更新失败")
	} else {
		log.Info("数据库心跳记录更新成功")
	}
}

func (s *Offline) startHeartbeat() {
	ticker := time.NewTicker(time.Second * 2)
	go func() {
		for range ticker.C {
			if s.HeartbeatStopFlag {
				break
			} else {
				s.heartbeat()
			}
		}
		ticker.Stop()
	}()
}

func (s *Offline) stopHeartbeat() {
	if !s.HeartbeatStopFlag {
		// 首次停止心跳需要更新数据库去注销当前的地址信息
		addr := ""
		err := db.UpdateAIMiniProgramSandbox(s.UserName, &addr, nil)
		if err != nil {
			log.With(log.Field{Key: "error", Value: err}).Error("停止心跳更新数据库失败")
		}
	}
	s.HeartbeatStopFlag = true
}

func (s *Offline) startJavaProxy() (err error) {
	if s.javaProxyCMD != nil {
		_ = s.javaProxyCMD.Process.Kill()
		_ = s.javaProxyCMD.Wait()
		s.javaProxyCMD = nil
	}
	s.javaProxyCMD = exec.CommandContext(
		context.Background(),
		"java",
		"-jar",
		"/home/<USER>/workspace/proxy.jar",
		"--spring.profiles.active=dev",
	)
	s.javaProxyCMD.SysProcAttr = &syscall.SysProcAttr{
		Setpgid: true,
	}
	s.javaProxyCMD.Stdout = os.Stdout
	s.javaProxyCMD.Stderr = os.Stderr
	err = s.javaProxyCMD.Start()
	return
}

func (s *Offline) waitForJavaProxyStarted() (err error) {
	begin := time.Now()
	timeout := time.Second * 30
	for {
		var conn net.Conn
		conn, err = net.DialTimeout("tcp", "127.0.0.1:8081", timeout)
		if err != nil {
			if time.Since(begin) > timeout {
				log.Error("java proxy 探活失败")
				return
			}
			time.Sleep(time.Millisecond * 100)
			continue
		}
		conn.Close()
		break
	}
	return
}

func (s *Offline) startJavaProxyAndEnsureStarted() {
	for {
		err := s.startJavaProxy()
		if err != nil {
			log.With(log.Field{Key: "error", Value: err}).Error("启动 java proxy 失败")
			time.Sleep(time.Second)
			continue
		}
		err = s.waitForJavaProxyStarted()
		if err != nil {
			log.With(log.Field{Key: "error", Value: err}).Error("启动 java proxy 等待失败")
			time.Sleep(time.Second)
			continue
		}
		break
	}
}

func (s *Offline) startWechat() (err error) {
	if s.wechatCMD != nil {
		_ = s.wechatCMD.Process.Kill()
		_ = s.wechatCMD.Wait()
		s.wechatCMD = nil
	}
	s.wechatCMD = exec.CommandContext(
		context.Background(),
		"/home/<USER>/workspace/wx/wechat",
		fmt.Sprintf("--wx_user=%s", s.UserName),
		fmt.Sprintf("--wx_password=%s", s.UserPassword),
	)
	s.wechatCMD.Env = append(
		os.Environ(),
		"http_proxy=http://mmminiprogramhttpproxy.production.polaris:11113",
		"https_proxy=http://mmminiprogramhttpproxy.production.polaris:11113",
		"NO_PROXY=localhost, 127.0.0.1/8, ::1, mirrors.tencent.com",
		"no_proxy=localhost, 127.0.0.1/8, ::1, mirrors.tencent.com",
	)
	s.wechatCMD.SysProcAttr = &syscall.SysProcAttr{
		Setpgid: true,
	}
	s.wechatCMD.Stdout = os.Stdout
	s.wechatCMD.Stderr = os.Stderr
	err = s.wechatCMD.Start()
	return
}

func (s *Offline) waitForWechatStarted() (err error) {
	begin := time.Now()
	timeout := time.Second * 30
	for {
		if _, err = GetWechatClient(); err != nil {
			if time.Since(begin) > timeout {
				log.Error("wechat 探活失败")
				return
			}
			time.Sleep(time.Millisecond * 100)
			continue
		}
		break
	}
	return
}

func (s *Offline) startWechatAndEnsureStarted() {
	for {
		err := s.startWechat()
		if err != nil {
			log.With(log.Field{Key: "error", Value: err}).Error("启动微信失败")
			time.Sleep(time.Second)
			continue
		}
		err = s.waitForWechatStarted()
		if err != nil {
			log.With(log.Field{Key: "error", Value: err}).Error("启动微信等待失败")
			time.Sleep(time.Second)
			continue
		}
		break
	}
}

func (s *Offline) initTestAccount() {
	testAccount, err := requestTestAccount()
	for {
		if err != nil {
			log.With(log.Field{Key: "error", Value: err}).Error("获取空闲测试账号失败")
			testAccount, err = requestTestAccount()
		} else {
			break
		}
	}
	s.UserID = testAccount.Data.ID
	s.UserName = testAccount.Data.Username
	s.UserPassword = testAccount.Data.Password
}
