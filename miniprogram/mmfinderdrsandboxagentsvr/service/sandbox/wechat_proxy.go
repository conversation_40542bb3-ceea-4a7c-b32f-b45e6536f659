// Package sandbox 沙箱背后的微信进程
package sandbox

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mmfinderdrsandboxagentsvr/model/api/base"
	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"
	sandbox_service_model "mmfinderdrsandboxagentsvr/model/service/sandbox"
	"net/http"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/gorilla/websocket"
)

// WechatProxy 沙箱背后的微信进程的实体类
type WechatProxy struct {
	DeadFlag                         bool
	executeInstructionLock           sync.Mutex
	miniProgramInstructionChannelID  atomic.Uint32
	miniProgramInstructionChannelMap sync.Map
	miniProgramWSConnMap             sync.Map
	wechatClient                     *sandbox_service_model.GetWechatClientsRespData
	wechatXWebWSConn                 *websocket.Conn
	wechatXWebInstructionChannelMap  sync.Map
}

// NewWechatProxy 创建一个单例的实体类对象
func NewWechatProxy() (w *WechatProxy) {
	w = &WechatProxy{}
	// 初始化变量
	if _, err := w.SetWechatClient(); err != nil {
		panic(err)
	}
	for _, v := range sandbox_service_model.XWebMethodArray {
		w.wechatXWebInstructionChannelMap.Store(v, make(chan []byte, 1))
	}
	log.Info("初始化变量成功")
	// 建立和微信的 websocket 长链接
	w.buildWechatWSConn()
	log.Info("建立和微信的 websocket 长链接成功")
	return
}

// GetWechatClient 探活微信进程
func GetWechatClient() (resp *sandbox_service_model.GetWechatClientsResp, err error) {
	httpResp, err := http.Get("http://127.0.0.1:8081/api/rd/get_clients")
	if err != nil {
		return
	}
	defer httpResp.Body.Close()
	body, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return
	}

	resp = &sandbox_service_model.GetWechatClientsResp{}
	if err = json.Unmarshal(body, resp); err != nil {
		return
	}
	tmp := resp.Data
	resp.Data = make([]*sandbox_service_model.GetWechatClientsRespData, 0)
	for i := 0; i < len(tmp); i++ {
		if tmp[i].Status == 1 {
			resp.Data = append(resp.Data, tmp[i])
		}
	}
	if len(resp.Data) == 0 {
		err = fmt.Errorf("GetWechatClientsRespData 为空")
	}
	return
}

// CleanAllWechatXWebInstructionChannel 清理所有管道无用的堆积消息
func (w *WechatProxy) CleanAllWechatXWebInstructionChannel(ctx context.Context) {
	w.wechatXWebInstructionChannelMap.Range(func(k, v interface{}) bool {
		chanV, _ := v.(chan []byte)
		select {
		case uselessMsg := <-chanV:
			log.WithContext(
				ctx,
				log.Field{Key: "message", Value: string(uselessMsg)},
			).Warn("开始执行下一个指令前为避免堆积消息导致的结果冲突，主动清理所有管道无用的堆积消息")
		default:
		}
		return true
	})
}

// ExecuteInstruction 操作微信
func (w *WechatProxy) ExecuteInstruction(
	ctx context.Context,
	req *sandbox_api_model.CDPProxyReq,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	return w.executeInstruction(ctx, req, false)
}

// ExecuteOnShareAppMessageCallback 获取分享链接的操作比较特殊，这里单独写一个方法来等待 XWeb.OnShareAppMessage 回调事件消息
func (w *WechatProxy) ExecuteOnShareAppMessageCallback(
	ctx context.Context,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	return w.waitXWebEventAndGenerateResp(ctx, "XWeb.OnShareAppMessage")
}

// Exit 优雅退出程序
func (w *WechatProxy) Exit() {
	w.DeadFlag = true
	w.wechatXWebWSConn.Close()
	w.miniProgramWSConnMap.Range(func(k, v interface{}) bool {
		conn, _ := v.(*websocket.Conn)
		conn.Close()
		return true
	})
	w.miniProgramWSConnMap.Clear()
}

// SetWechatClient 设置微信进程的 client 信息，内含 token 信息（注意这个 token 和 authCode 的区别）
func (w *WechatProxy) SetWechatClient() (resp *sandbox_service_model.GetWechatClientsResp, err error) {
	resp, err = GetWechatClient()
	if err == nil {
		w.wechatClient = resp.Data[0]
	}
	return
}

func (w *WechatProxy) addChan() (uint32, chan []byte) {
	id := w.miniProgramInstructionChannelID.Add(1)
	chanV := make(chan []byte, 1)
	w.miniProgramInstructionChannelMap.Store(id, chanV)
	return id, chanV
}

func (w *WechatProxy) deleteChan(id uint32) {
	w.miniProgramInstructionChannelMap.Delete(id)
}

func (w *WechatProxy) postChan(id uint32, msg []byte) {
	v, ok := w.miniProgramInstructionChannelMap.LoadAndDelete(id)
	if ok {
		log.With(log.Field{Key: "message", Value: string(msg)}).Info("往输出管道中写入回调事件消息")
		chanV, _ := v.(chan []byte)
		chanV <- msg
	} else {
		log.With(
			log.Field{Key: "message", Value: string(msg)},
			log.Field{Key: "event_id", Value: id},
		).Error("event_id 对应的管道不存在")
	}
}

func (w *WechatProxy) initWSConn(url string) (conn *websocket.Conn, err error) {
	conn, _, err = websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		errMsg := "建立 websocket 长链接失败"
		log.With(
			log.Field{Key: "url", Value: url},
			log.Field{Key: "error", Value: err},
		).Error(errMsg)
		err = fmt.Errorf("%s：url=%s，error=%s", errMsg, url, err)
		return
	}
	go func() {
		begin := time.Now()
		refreshTimeout := time.Second * 1
		failedCount := 0
		for {
			if w.DeadFlag {
				conn.Close()
				break
			}
			_, msg, err := conn.ReadMessage()
			if err != nil {
				log.With(log.Field{Key: "error", Value: err}).Error("读取回调事件消息失败")
				if time.Since(begin) > refreshTimeout {
					failedCount = 0
					begin = time.Now()
				} else {
					time.Sleep(time.Millisecond * 100)
				}
				failedCount++
				if failedCount > 3 {
					log.With(log.Field{Key: "url", Value: url}).Error("读取回调事件消息失败的次数过多，关闭连接")
					break
				}
				continue
			}
			event := &sandbox_service_model.WechatCDPBaseEvent{}
			if err = json.Unmarshal(msg, event); err != nil {
				log.With(
					log.Field{Key: "message", Value: string(msg)},
					log.Field{Key: "error", Value: err},
				).Error("json 解析回调事件消息失败")
				continue
			}
			if strings.HasPrefix(event.Event, "XWeb.") {
				const skipEvent = "XWeb.OnJsApiCallback"
				if event.Event == skipEvent {
					// 这个回调事件消息太多了，且不同的小程序有不同的情况，所以直接跳过该回调事件消息的处理
					continue
				}
				v, ok := w.wechatXWebInstructionChannelMap.Load(event.Event)
				if ok {
					log.With(log.Field{Key: "message", Value: string(msg)}).Info("往输出管道中写入回调事件消息")
					chanV, _ := v.(chan []byte)
					select {
					case uselessMsg := <-chanV:
						// 如果管道里面已经有数据了的话，需要先清理出来，否则会导致阻塞
						// 堆积原因是每次指令的下发执行后，并不会都处理该指令产生的所有回调事件消息
						log.With(log.Field{Key: "message", Value: string(uselessMsg)}).Warn("清理管道无用的堆积消息")
					default:
					}
					chanV <- msg
				} else {
					log.With(log.Field{Key: "message", Value: string(msg)}).Error("未知的 XWeb 回调事件消息")
				}
				continue
			}
			if event.ID == nil {
				log.With(log.Field{Key: "message", Value: string(msg)}).Error("回调事件消息的 event_id 不存在")
				continue
			}
			w.postChan(*event.ID, msg)
		}
	}()
	return
}

func (w *WechatProxy) buildWechatWSConn() {
	url := fmt.Sprintf("ws://127.0.0.1:8081/rd/client/%s?role=server", w.wechatClient.Token)
	var err error
	w.wechatXWebWSConn, err = w.initWSConn(url)
	for err != nil {
		w.wechatXWebWSConn, err = w.initWSConn(url)
	}
}

func (w *WechatProxy) getMiniProgramWSConn(
	ctx context.Context,
	targetID string,
) (conn *websocket.Conn, err error) {
	v, ok := w.miniProgramWSConnMap.Load(targetID)
	if ok {
		conn, _ = v.(*websocket.Conn)
	} else {
		// 判断小程序页面是否存在
		req := &sandbox_api_model.CDPProxyReq{
			Method: "XWeb.inspect",
			Params: map[string]interface{}{
				"role":        "server",
				"targetId":    targetID,
				"deviceToken": w.wechatClient.Token,
			},
		}
		_, err = w.executeInstruction(ctx, req, true)
		if err != nil {
			return
		}
		// 创建小程序页面的 websocket 长链接
		url := fmt.Sprintf("ws://127.0.0.1:8081/rd/page/%s?role=server&token=%s", targetID, w.wechatClient.Token)
		conn, err = w.initWSConn(url)
		if err == nil {
			w.miniProgramWSConnMap.Store(targetID, conn)
		}
	}
	return
}

func (w *WechatProxy) executeInstruction(
	ctx context.Context,
	req *sandbox_api_model.CDPProxyReq,
	skipLock bool,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	if w.DeadFlag {
		errMsg := "wechat 已关闭"
		log.WithContext(ctx).Error(errMsg)
		err = fmt.Errorf("%s", errMsg)
		return
	}
	if !skipLock {
		w.executeInstructionLock.Lock()
		defer w.executeInstructionLock.Unlock()
	}
	// 根据 TargetId 字段判断是微信的 XWeb 操作还是小程序的操作
	if req.TargetID == nil {
		resp, err = w.executeWechatXWebInstruction(ctx, req)
	} else {
		resp, err = w.executeMiniProgramInstruction(ctx, req)
	}
	return
}

func (w *WechatProxy) executeWechatXWebInstruction(
	ctx context.Context,
	req *sandbox_api_model.CDPProxyReq,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	messageMap := map[string]any{
		"method": req.Method,
		"params": req.Params,
	}
	message, err := json.Marshal(&messageMap)
	if err != nil {
		return
	}
	log.WithContext(ctx, log.Field{Key: "message", Value: string(message)}).Info("执行微信的 XWeb 操作")
	// 需要先清理所有管道无用的堆积消息
	w.CleanAllWechatXWebInstructionChannel(ctx)
	if err = w.wechatXWebWSConn.WriteMessage(websocket.TextMessage, []byte(message)); err != nil {
		return
	}
	// 不同的指令需要不同的操作逻辑来处理回调事件消息
	switch req.Method {
	case "XWeb.LaunchApplet":
		resp, err = w.executeLaunchAppletCallback(ctx)
	case "XWeb.RequestLogin":
		resp, err = w.executeRequestLoginCallback(ctx)
	case "XWeb.RequestLogout":
		resp, err = w.executeRequestLogoutCallback(ctx)
	case "XWeb.GetAppletPageInfo":
		resp, err = w.executeGetAppletPageInfoCallback(ctx)
	// 仅输出的指令
	case "XWeb.OnLaunchApplet",
		"XWeb.OnAppletReady",
		"XWeb.OnShareAppMessage",
		"XWeb.OnJsApiCallback",
		"XWeb.OnLoginResult",
		"XWeb.OnLoginFinish",
		"XWeb.OnLogoutResult",
		"XWeb.OnGetAppletPageInfo":
		err = fmt.Errorf("不支持的 XWeb 指令")
	// 输入输出都有的指令使用 default 逻辑即可
	// case "XWeb.CloseApplet":
	// case "XWeb.targets":
	// case "XWeb.inspect":
	// case "XWeb.NetLogStart":
	// case "XWeb.NetLogStop":
	// 仅输入的指令没有输出所以 mock 一个空数据返回
	case "XWeb.SetLocation":
		resp = &base.Resp[sandbox_api_model.CDPProxyRespData]{
			Data: &sandbox_api_model.CDPProxyRespData{},
		}
	default:
		resp, err = w.waitXWebEventAndGenerateResp(ctx, req.Method)
	}
	if err != nil {
		err = fmt.Errorf("%s，message=%s", err, message)
	}
	return
}

func (w *WechatProxy) executeMiniProgramInstruction(
	ctx context.Context,
	req *sandbox_api_model.CDPProxyReq,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	id, chanV := w.addChan()
	messageMap := map[string]any{
		"id":     id,
		"method": req.Method,
		"params": req.Params,
	}
	message, err := json.Marshal(&messageMap)
	if err != nil {
		return
	}
	conn, err := w.getMiniProgramWSConn(ctx, *req.TargetID)
	if err != nil {
		return
	}
	log.WithContext(ctx, log.Field{Key: "message", Value: string(message)}).Info("执行小程序的操作")
	if err = conn.WriteMessage(websocket.TextMessage, []byte(message)); err != nil {
		return
	}
	select {
	case msg := <-chanV:
		resp = &base.Resp[sandbox_api_model.CDPProxyRespData]{
			Data: &sandbox_api_model.CDPProxyRespData{},
		}
		err = json.Unmarshal(msg, resp.Data)
	case <-time.After(5 * time.Second):
		w.deleteChan(id)
		errMsg := "等待 event_id 的回调事件消息超时"
		log.WithContext(ctx, log.Field{Key: "event_id", Value: id}).Error(errMsg)
		err = fmt.Errorf("%s：event_id=%d", errMsg, id)
	}
	return
}

func (w *WechatProxy) executeLaunchAppletCallback(
	ctx context.Context,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	// 1. 等待回调事件消息 XWeb.OnLaunchApplet
	_, err = w.waitXWebEventAndGenerateResp(ctx, "XWeb.OnLaunchApplet")
	if err != nil {
		return
	}
	// 2. 等待回调事件消息 XWeb.OnAppletReady
	return w.waitXWebEventAndGenerateResp(ctx, "XWeb.OnAppletReady")
}

func (w *WechatProxy) executeRequestLoginCallback(
	ctx context.Context,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	// 1. 等待回调事件消息 XWeb.OnLoginResult
	respForOnLoginResult, err := w.waitXWebEventAndGenerateResp(ctx, "XWeb.OnLoginResult")
	if err != nil {
		return
	}
	dataMap, _ := respForOnLoginResult.Data.Data.(map[string]interface{})
	errorCode, ok := dataMap["error"].(float64)
	if !ok || int(errorCode) != 0 {
		messageForOnLoginResult, _ := json.Marshal(respForOnLoginResult)
		errMsg := "XWeb.OnLoginResult 显示登陆失败"
		log.WithContext(
			ctx,
			log.Field{Key: "ok", Value: ok},
			log.Field{Key: "error_code", Value: errorCode},
			log.Field{Key: "message", Value: string(messageForOnLoginResult)},
		).Error(errMsg)
		err = fmt.Errorf("%s：ok=%t，error_code=%g，message=%s", errMsg, ok, errorCode, string(messageForOnLoginResult))
		return
	}
	// 2. 等待回调事件消息 XWeb.OnLoginFinish
	return w.waitXWebEventAndGenerateResp(ctx, "XWeb.OnLoginFinish")
}

func (w *WechatProxy) executeRequestLogoutCallback(
	ctx context.Context,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	resp, err = w.waitXWebEventAndGenerateResp(ctx, "XWeb.OnLogoutResult")
	if err == nil {
		// 账户注销后，所有小程序的链接可以关闭了
		w.miniProgramWSConnMap.Range(func(k, v interface{}) bool {
			conn, _ := v.(*websocket.Conn)
			conn.Close()
			return true
		})
		w.miniProgramWSConnMap.Clear()
	}
	return
}

func (w *WechatProxy) executeGetAppletPageInfoCallback(
	ctx context.Context,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	return w.waitXWebEventAndGenerateResp(ctx, "XWeb.OnGetAppletPageInfo")
}

func (w *WechatProxy) waitXWebEventAndGenerateResp(
	ctx context.Context,
	eventType string,
) (resp *base.Resp[sandbox_api_model.CDPProxyRespData], err error) {
	v, ok := w.wechatXWebInstructionChannelMap.Load(eventType)
	if !ok {
		err = fmt.Errorf("不支持的 XWeb 回调事件消息类型 %s", eventType)
		return
	}
	chanV, _ := v.(chan []byte)
	select {
	case msg := <-chanV:
		log.WithContext(ctx, log.Field{Key: "message", Value: string(msg)}).Info("成功获取 XWeb 回调事件消息")
		resp = &base.Resp[sandbox_api_model.CDPProxyRespData]{
			Data: &sandbox_api_model.CDPProxyRespData{},
		}
		err = json.Unmarshal(msg, resp.Data)
	case <-time.After(5 * time.Second):
		errMsg := "等待 XWeb 回调事件消息超时"
		log.WithContext(ctx, log.Field{Key: "event_type", Value: eventType}).Error(errMsg)
		err = fmt.Errorf("%s：event_type=%s", errMsg, eventType)
	}
	return
}
