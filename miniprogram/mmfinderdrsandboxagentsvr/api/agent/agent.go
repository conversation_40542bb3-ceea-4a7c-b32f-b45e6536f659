// Package agent 定义相关的接口
package agent

import (
	"fmt"
	"time"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/core/router"

	agent_api_model "mmfinderdrsandboxagentsvr/model/api/agent"
	"mmfinderdrsandboxagentsvr/model/api/base"
	agent_service "mmfinderdrsandboxagentsvr/service/agent"
	"mmfinderdrsandboxagentsvr/util"
)

func agentRun(ctx iris.Context) {
	req := &agent_api_model.RunReq{}
	req.Position = &agent_api_model.PositionInfo{
		Latitude:  -1,
		Longitude: -1,
	}
	resp := &base.Resp[agent_api_model.BaseRespData]{
		Data: &agent_api_model.BaseRespData{},
	}
	if err := ctx.ReadJSON(req); err != nil || req.AppID == "" || req.AuthCode == "" {
		ctx.StatusCode(iris.StatusBadRequest)
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	if err := util.ExecuteFunctionInQueueWithTimeout(time.Second*45, func() {
		args, envs := req.Args()
		var err error
		resp, err = agent_service.Run(
			ctx, req.AuthCode, req.Position, req.SkipShareURLData, args, envs,
		)
		if err != nil {
			ctx.StatusCode(iris.StatusInternalServerError)
		}
		_ = ctx.JSON(resp)
	}); err != nil {
		// 如果被锁 block 住太久则直接返回
		ctx.StatusCode(iris.StatusTooManyRequests)
		resp.SetErr(ctx, iris.StatusTooManyRequests, fmt.Errorf("系统繁忙，请稍后再试"))
		_ = ctx.JSON(resp)
		return
	}
}

func agentRunHeadless(ctx iris.Context) {
	req := &agent_api_model.RunHeadlessReq{}
	resp := &base.Resp[agent_api_model.BaseRespData]{
		Data: &agent_api_model.BaseRespData{},
	}
	if err := ctx.ReadJSON(req); err != nil || req.AppID == "" {
		ctx.StatusCode(iris.StatusBadRequest)
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}
	// 无头不应该有锁控制
	args, envs := req.Args()
	var err error
	resp, err = agent_service.Run(ctx, "", nil, true, args, envs)
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
	}
	_ = ctx.JSON(resp)
}

// Init 注册 agent 相关的路由
func Init(app router.Party) {
	api := app.Party("/v1/agent")
	api.Post("/run", agentRun)
	api.Post("/run_headless", agentRunHeadless)
}
