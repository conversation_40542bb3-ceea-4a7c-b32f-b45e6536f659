package api

import (
	"mmfinderdrsandboxagentsvr/env"

	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/kataras/iris/v12"
)

// HeaderMiddleware 给响应增加 header
func HeaderMiddleware(ctx iris.Context) {
	ctx.Header("x-sandbox-ip", env.IP)
	ctx.Next()
}

// LogMiddleware iris 日志中间件
func LogMiddleware(ctx iris.Context) {
	workflowRunID := ctx.GetHeader("x-workflow-run-id")
	cc, msg := codec.EnsureMessage(ctx.Request().Context())
	msg.WithLogger(log.GetDefaultLogger().With(log.Field{Key: "workflow_run_id", Value: workflowRunID}))
	ctx.ResetRequest(ctx.Request().WithContext(cc))
	ctx.Next()
}
