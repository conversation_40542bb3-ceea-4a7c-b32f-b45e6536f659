// Package sandbox 定义相关的接口
package sandbox

import (
	"fmt"
	"mmfinderdrsandboxagentsvr/model/api/base"
	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"
	sandbox_service "mmfinderdrsandboxagentsvr/service/sandbox"
	"mmfinderdrsandboxagentsvr/util"
	"strconv"
	"strings"
	"time"

	"github.com/kataras/iris/v12"
)

// createSandbox 创建沙箱接口
func createSandbox(ctx iris.Context) {
	req := &sandbox_api_model.CreateSandboxReq{}
	resp := &base.Resp[sandbox_api_model.CreateSandboxRespData]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	if err := util.ExecuteFunctionInQueueWithTimeout(time.Second*45, func() {
		var err error
		resp, err = sandbox_service.CreateSandbox(ctx, req)
		if err != nil {
			resp = &base.Resp[sandbox_api_model.CreateSandboxRespData]{}
			resp.SetErr(ctx, iris.StatusInternalServerError, err)
			_ = ctx.JSON(resp)
			return
		}
		_ = ctx.JSON(resp)
	}); err != nil {
		// 如果被锁 block 住太久则直接返回
		resp := &base.Resp[sandbox_api_model.CreateSandboxRespData]{}
		resp.SetErr(ctx, iris.StatusTooManyRequests, fmt.Errorf("系统繁忙，请稍后再试"))
		_ = ctx.JSON(resp)
		return
	}

}

// sandboxAction 沙箱交互步骤接口
func sandboxAction(ctx iris.Context) {
	req := &sandbox_api_model.ActionReq{}
	resp := &base.Resp[sandbox_api_model.ActionRespData]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	if err := util.ExecuteFunctionInQueueWithTimeout(time.Second*45, func() {
		var err error
		resp, err = sandbox_service.ExecuteSandboxAction(ctx, req)
		if err != nil {
			resp = &base.Resp[sandbox_api_model.ActionRespData]{}
			resp.SetErr(ctx, iris.StatusInternalServerError, err)
			_ = ctx.JSON(resp)
			return
		}
		_ = ctx.JSON(resp)
	}); err != nil {
		// 如果被锁 block 住太久则直接返回
		resp := &base.Resp[sandbox_api_model.ActionRespData]{}
		resp.SetErr(ctx, iris.StatusTooManyRequests, fmt.Errorf("系统繁忙，请稍后再试"))
		_ = ctx.JSON(resp)
		return
	}

}

// killSandbox 关闭沙箱接口
func killSandbox(ctx iris.Context) {
	req := &sandbox_api_model.KillSandboxReq{}
	resp := &base.Resp[any]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	if err := util.ExecuteFunctionInQueueWithTimeout(time.Second*45, func() {
		var err error
		resp, err = sandbox_service.KillSandbox(ctx, req)
		if err != nil {
			resp = &base.Resp[any]{}
			// if error starts with "等待 XWeb 回调事件消息超时", it means sandbox has already been killed
			if strings.HasPrefix(err.Error(), "等待 XWeb 回调事件消息超时") {
				resp.Message = "success"
				_ = ctx.JSON(resp)
				return
			}
			resp.SetErr(ctx, iris.StatusInternalServerError, err)
			_ = ctx.JSON(resp)
			return
		}
		_ = ctx.JSON(resp)
	}); err != nil {
		// 如果被锁 block 住太久则直接返回
		resp := &base.Resp[any]{}
		resp.SetErr(ctx, iris.StatusTooManyRequests, fmt.Errorf("系统繁忙，请稍后再试"))
		_ = ctx.JSON(resp)
		return
	}

}

// submitAnnotation 提交标注内容接口
func submitAnnotation(ctx iris.Context) {
	req := &sandbox_api_model.SubmitAnnotationReq{}
	resp := &base.Resp[any]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	resp, err := sandbox_service.SubmitAnnotation(req)
	if err != nil {
		resp = &base.Resp[any]{}
		resp.SetErr(ctx, iris.StatusInternalServerError, err)
		_ = ctx.JSON(resp)
		return
	}

	_ = ctx.JSON(resp)
}

// parseRTXValues 解析RTX参数，支持逗号分隔的多个值
func parseRTXValues(ctx iris.Context) []string {
	rtxValues := make([]string, 0)
	rtxParam := ctx.URLParam("rtx")

	if strings.Contains(rtxParam, ",") {
		values := strings.Split(rtxParam, ",")
		for _, v := range values {
			trimmed := strings.TrimSpace(v)
			if trimmed != "" {
				rtxValues = append(rtxValues, trimmed)
			}
		}
	} else if rtxParam != "" {
		rtxValues = append(rtxValues, rtxParam)
	}

	return rtxValues
}

// parseIntParam 解析整数参数，如果解析失败则返回默认值
func parseIntParam(ctx iris.Context, paramName string, defaultValue int) int {
	paramStr := ctx.URLParam(paramName)
	if paramStr != "" {
		if parsed, err := strconv.Atoi(paramStr); err == nil {
			return parsed
		}
	}
	return defaultValue
}

// parseInt8PtrParam 解析可选的int8参数，返回指针类型
func parseInt8PtrParam(ctx iris.Context, paramName string) *int8 {
	paramStr := ctx.URLParam(paramName)
	if paramStr != "" {
		if parsed, err := strconv.Atoi(paramStr); err == nil {
			val := int8(parsed)
			return &val
		}
	}
	return nil
}

// parseTimeParams 解析时间范围参数
func parseTimeParams(ctx iris.Context) (*time.Time, *time.Time) {
	const timeLayout = "2006-01-02 15:04:05"
	var startTime, endTime *time.Time

	startTimeStr := ctx.URLParam("startTime")
	if startTimeStr != "" {
		if parsedTime, err := time.Parse(timeLayout, startTimeStr); err == nil {
			startTime = &parsedTime
		}
	}

	endTimeStr := ctx.URLParam("endTime")
	if endTimeStr != "" {
		if parsedTime, err := time.Parse(timeLayout, endTimeStr); err == nil {
			endTime = &parsedTime
		}
	}

	return startTime, endTime
}

// getAnnotations 获取标注内容列表接口
func getAnnotations(ctx iris.Context) {
	rtxValues := parseRTXValues(ctx)
	appID := ctx.URLParam("appId")
	targetID := ctx.URLParam("targetId")
	instruction := ctx.URLParam("instruction")

	limit := parseIntParam(ctx, "limit", 10)
	offset := parseIntParam(ctx, "offset", 0)

	isDeleted := parseInt8PtrParam(ctx, "isDeleted")
	isEval := parseInt8PtrParam(ctx, "isEval")
	source := parseInt8PtrParam(ctx, "source")

	startTime, endTime := parseTimeParams(ctx)

	resp, err := sandbox_service.GetAnnotations(rtxValues, appID, targetID, instruction,
		limit, offset, isDeleted, isEval, source, startTime, endTime)
	if err != nil {
		resp = &base.Resp[sandbox_api_model.GetAnnotationsRespData]{}
		resp.SetErr(ctx, iris.StatusInternalServerError, err)
		_ = ctx.JSON(resp)
		return
	}

	_ = ctx.JSON(resp)
}

// deleteAnnotation 软删除标注内容接口
func deleteAnnotation(ctx iris.Context) {
	req := &sandbox_api_model.DeleteAnnotationReq{}
	resp := &base.Resp[any]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	resp, err := sandbox_service.DeleteAnnotation(req)
	if err != nil {
		resp = &base.Resp[any]{}
		resp.SetErr(ctx, iris.StatusInternalServerError, err)
		_ = ctx.JSON(resp)
		return
	}

	_ = ctx.JSON(resp)
}

// deleteAnnotationOperation 软删除标注操作记录接口
func deleteAnnotationOperation(ctx iris.Context) {
	req := &sandbox_api_model.DeleteAnnotationOperationReq{}
	resp := &base.Resp[any]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	resp, err := sandbox_service.DeleteAnnotationOperation(req)
	if err != nil {
		resp = &base.Resp[any]{}
		resp.SetErr(ctx, iris.StatusInternalServerError, err)
		_ = ctx.JSON(resp)
		return
	}

	_ = ctx.JSON(resp)
}

func evalAnnotation(ctx iris.Context) {
	req := &sandbox_api_model.EvalAnnotationReq{}
	resp := &base.Resp[any]{}

	if err := ctx.ReadJSON(req); err != nil {
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}

	resp, err := sandbox_service.EvalAnnotation(req)
	if err != nil {
		resp = &base.Resp[any]{}
		resp.SetErr(ctx, iris.StatusInternalServerError, err)
		_ = ctx.JSON(resp)
		return
	}

	_ = ctx.JSON(resp)
}
