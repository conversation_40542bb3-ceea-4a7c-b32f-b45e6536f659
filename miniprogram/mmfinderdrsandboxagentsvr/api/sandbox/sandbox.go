// Package sandbox 定义相关的接口
package sandbox

import (
	"mmfinderdrsandboxagentsvr/env"
	"mmfinderdrsandboxagentsvr/model/api/base"
	sandbox_api_model "mmfinderdrsandboxagentsvr/model/api/sandbox"
	sandbox_service "mmfinderdrsandboxagentsvr/service/sandbox"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/core/router"
)

func authCode(ctx iris.Context) {
	resp := &base.Resp[sandbox_api_model.AuthCodeRespData]{}
	uin := ctx.URLParam("uin")
	data, err := sandbox_service.SandboxOnlineObj.GetAuthCode(ctx, uin)
	if err != nil {
		ctx.StatusCode(iris.StatusInternalServerError)
		resp.SetErr(ctx, iris.StatusInternalServerError, err)
		_ = ctx.JSON(resp)
		return
	}
	resp.Data = &data
	_ = ctx.JSON(resp)
}

func cdpProxy(ctx iris.Context) {
	resp := &base.Resp[sandbox_api_model.CDPProxyRespData]{}
	req := &sandbox_api_model.CDPProxyReq{}
	if err := ctx.ReadJSON(req); err != nil {
		ctx.StatusCode(iris.StatusBadRequest)
		resp.SetErr(ctx, iris.StatusBadRequest, err)
		_ = ctx.JSON(resp)
		return
	}
	// 调用服务层执行操作
	resp, err := sandbox_service.SandboxOnlineObj.ExecuteInstruction(ctx, req)
	if err != nil {
		resp = &base.Resp[sandbox_api_model.CDPProxyRespData]{}
		ctx.StatusCode(iris.StatusInternalServerError)
		resp.SetErr(ctx, iris.StatusInternalServerError, err)
		_ = ctx.JSON(resp)
		return
	}
	_ = ctx.JSON(resp)
}

func cliHeartbeated(ctx iris.Context) {
	resp := &base.Resp[any]{}
	_ = ctx.JSON(resp)
}

func userCli(ctx iris.Context) {
	resp := &base.Resp[sandbox_api_model.UserCliRespData]{
		Data: &sandbox_api_model.UserCliRespData{
			ID: -1,
			IP: env.IP,
		},
	}
	_, _ = sandbox_service.SandboxOnlineObj.Logout(ctx)
	_ = ctx.JSON(resp)
}

// Init 初始化
func Init(app router.Party) {
	api := app.Party("/v1/sandbox")
	api.Get("/auth_code", authCode)
	api.Post("/cdp_proxy/{userId}", cdpProxy)
	api.Get("/cli_heartbeated/{userId}", cliHeartbeated)
	api.Get("/user_cli", userCli)

	// 初始化沙箱标注相关的路由
	annotationsAPI := app.Party("/v1/sandbox/annotations")
	annotationsAPI.Post("/create", createSandbox)
	annotationsAPI.Post("/action", sandboxAction)
	annotationsAPI.Post("/kill", killSandbox)
	annotationsAPI.Post("/submit", submitAnnotation)
	annotationsAPI.Post("/delete", deleteAnnotation)
	annotationsAPI.Post("/eval", evalAnnotation)
	annotationsAPI.Post("/delete_operation", deleteAnnotationOperation)
	annotationsAPI.Get("/", getAnnotations)

	// 老接口兼容
	app.Post("/wx_sandbox_agent/cdp_proxy/{userId}", cdpProxy)
	app.Get("/wx_test_account/cli_heartbeated/{userId}", cliHeartbeated)
	app.Get("/wx_test_account/user_cli", userCli)
}
