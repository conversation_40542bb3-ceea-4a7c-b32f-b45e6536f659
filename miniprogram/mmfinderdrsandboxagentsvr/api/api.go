// Package api 初始化所有接口
package api

import (
	"mmfinderdrsandboxagentsvr/api/agent"
	"mmfinderdrsandboxagentsvr/api/ops"
	"mmfinderdrsandboxagentsvr/api/sandbox"
	sandbox_service "mmfinderdrsandboxagentsvr/service/sandbox"

	"github.com/kataras/iris/v12"
	"github.com/kataras/iris/v12/middleware/recover"
	"github.com/kataras/iris/v12/middleware/requestid"
)

// Init 初始化
func Init() {
	// 初始化沙箱
	sandbox_service.InitSandboxOnline()
	// 初始化接口
	app := iris.New()
	app.Logger().SetLevel("info")
	// 添加中间件
	app.UseRouter(requestid.New())
	app.UseRouter(recover.New())
	app.Use(iris.Compression)
	app.Use(HeaderMiddleware)
	app.Use(LogMiddleware)
	// init each api router
	apiV1 := app.Party("/")
	agent.Init(apiV1)
	ops.Init(apiV1)
	sandbox.Init(apiV1)
	// 启动服务
	err := app.Listen(":80")
	if err != nil {
		panic(err)
	}
}

// Exit 优雅退出程序
func Exit() {
	// sandbox_service.SandboxOnlineObj.Exit()
}
