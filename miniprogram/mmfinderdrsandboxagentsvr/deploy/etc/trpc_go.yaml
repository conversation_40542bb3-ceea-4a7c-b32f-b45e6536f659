global: #全局配置
  namespace: Production #环境类型，分正式 Production 和非正式 Development 两种类型
  env_name: online #环境名称，非正式环境下多环境的名称

server: #服务端配置
  app: mmfinderdr #业务的应用名
  server: mmfinderdrsandboxagentsvr #进程服务名
  bin_path: /home/<USER>/mmfinderdrsandboxagentsvr/bin/ #二进制可执行文件和框架配置文件所在路径
  conf_path: /home/<USER>/mmfinderdrsandboxagentsvr/etc/ #业务配置文件所在路径
  data_path: /home/<USER>/mmfinderdrsandboxagentsvr/sbin/ #业务数据文件所在路径
  filter: #针对所有service处理函数前后的拦截器列表
  service: #业务服务提供的service，可以有多个

client: #客户端调用的后端配置
  timeout: 1000 #针对所有后端的请求最长处理时间
  namespace: Production #针对所有后端的环境
  filter: #针对所有后端调用函数前后的拦截器列表
  service: #针对单个后端的配置
    - name: trpc.http.mmfinderdrsandboxsvrkithelper.common
      target: polaris://mmfinderdrsandboxsvrkithelper
      protocol: http
      serialization: 2
      timeout: 1000

plugins: #插件配置
  log: #日志配置
    default: #默认日志的配置，可支持多输出
      - writer: file #本地文件日志
        level: debug #本地文件滚动日志的级别
        # formatter: json
        writer_config:
          filename: /home/<USER>/log/mmfinderdrsandboxagentsvr/server.log #本地文件滚动日志存放的路径
          max_size: 300 #本地文件滚动日志的大小 单位 MB
          max_backups: 30 #最大日志文件数
          max_age: 7 #最大日志保留天数
          compress: false #日志文件是否压缩
      - writer: wecube
        level: info
        remote_config:
          biz_id: 16225
  selector: #针对trpc框架服务发现的配置
    polaris: #北极星服务发现的配置
      #debug: true                                   #开启 debug 日志
      #enable_canary: false                          #开启金丝雀功能，默认 false 不开启
      #timeout: 1000                                 #单位 ms，默认 1000ms，北极星获取实例接口的超时时间
      #report_timeout: 1ms                           #默认1ms，如果设置了，则下游超时，并且少于设置的值，则忽略错误不上报
      #connect_timeout: 1000                         #单位 ms，默认 1000ms，连接北极星后台服务的超时时间
      #message_timeout: 1s                           #类型为 time.Duration，从北极星后台接收一个服务信息的超时时间，默认为1s
      #log_dir: $HOME/polaris/log                    #北极星日志目录
      protocol: grpc #名字服务远程交互协议类型
