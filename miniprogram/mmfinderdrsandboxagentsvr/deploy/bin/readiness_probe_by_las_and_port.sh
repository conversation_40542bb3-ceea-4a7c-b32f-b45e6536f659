#!/bin/sh

# 健康检查脚本
# 如果curl请求返回HTTP 200且code为0，则脚本返回0，否则返回1

response=$(curl -s -o /dev/null -w "%{http_code}" http://127.0.0.1/v1/ops/health)

if [ "$response" -eq 200 ]; then
    # 使用grep提取code值，不依赖jq
    json_response=$(curl -s http://127.0.0.1/v1/ops/health)
    code=$(echo "$json_response" | grep -o '"code":[ ]*[0-9]*' | cut -d':' -f2 | tr -d ' ')
    
    # 检查code是否为数字（兼容/bin/sh的写法）
    case "$code" in
        ''|*[!0-9]*) 
            exit 1  # 不是有效数字
            ;;
    esac
    
    if [ "$code" -eq 0 ]; then
        echo 0
        exit 0  # 健康检查通过
    else
        echo 1  
        exit 1  # 返回了200但code不是0
    fi
else
    echo 1
    exit 1  # HTTP状态码不是200
fi
