## 1. 开发环境 Get Started
### 1.1 使用 Anaconda 安装 python 3.12

```
conda create -n py312 python=3.12
source activate py312
```

### 1.2 安装 VS Code 插件

```
# 必须安装的插件
"ms-python.python",
"ms-python.autopep8",
"ms-python.pylint",
# 可选择安装的插件
"mhutchie.git-graph",
"donjayamanne.githistory",
"eamodio.gitlens",
"github.copilot",
"yzane.markdown-pdf",
"ms-vscode-remote.remote-ssh"
```

### 1.3 依赖管理 poetry

```
python -m pip install poetry==2.1.3
poetry install
```

### 1.4 pre-commit

```
# Install pre-commit
python -m pip install pre-commit
# 在这个整个仓库的根目录下（即 .git 所在的目录）运行下面这个命令
pre-commit install --config miniprogram/mmfinderdrsandboxagentsvr/deploy/pyfile/.pre-commit-config.yaml
```


## 2. 代码风格
### 规则

- 必须按照该文档安装 VS Code 的插件和 pre-commit
- 函数的出入参要有类型声明

### 目录结构

- script：测试脚本
- sdk：wxms sdk
- task_handler：各个任务类型的算法推理逻辑
- util：各个工具类
- .pre-commit-config.yaml：pre-commit 的配置
- main.py：主函数入口
- model.py：数据模型定义
- poetry.lock：poetry lock 文件
- pyproject.toml：project 依赖包的配置
- README.md：该文档


## 其他
### poetry 的注意事项

其他可能会用到的命令

```
# 增加依赖
poetry add {依赖名}
 
# 安装依赖
poetry install

# 更新依赖
poetry update {依赖名}

# 删除依赖
poetry remove {依赖名}

# 环境信息
poetry env info

# 展示依赖版本
poetry show -t
```

如果 poetry.lock 文件冲突，应该删除 `rm poetry.lock` 并使用 `poetry update` 重新生成 poetry.lock 文件

### sdk 发版

```
cd ./sdk
# 修改 sdk pyproject.toml 中的 version
./build.sh
cd ..
# 修改 pyfile pyproject.toml sdk 的 version
rm poetry.lock
poetry update
# 推送代码即可自动触发镜像构建
```
