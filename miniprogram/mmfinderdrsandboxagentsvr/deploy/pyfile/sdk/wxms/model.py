"""
定义各种类型
"""

APPLET_FIXED_HEIGHT = 776
APPLET_FIXED_WIDTH = 410
INTERRUPT_ERROR_CODE_DICT = {
    -1: "小程序不在前台",
    -2: "小程序不在 agent 模式（通过非 launchApplet 的形式启动过小程序）",
    -3: "命令未绑定到小程序",
    -4: "小程序 webview 不存在",
    -5: "命令运行错误",
    -6: "命令参数错误",
}


class InterruptError(Exception):
    """
    中断错误，调用方需要处理该错误用于中断推理过程
    """

    def __init__(self, msg="interrupted", params=None):
        self.msg = msg
        super().__init__(self.msg)
        self.params = params
