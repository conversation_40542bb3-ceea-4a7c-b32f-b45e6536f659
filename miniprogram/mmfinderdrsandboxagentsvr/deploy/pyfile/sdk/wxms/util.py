"""
工具类
"""

import io
import logging
import os

from PIL import Image, ImageOps
from py_mini_racer import MiniRacer

from .js_code import js_code_for_unzip_xml, js_code_xbar
from .model import APPLET_FIXED_HEIGHT, APPLET_FIXED_WIDTH


def decode_csv_to_xml(csv_data: str) -> str:
    """
    解压微信客户端上报数据的 csv 格式为 xml 格式
    """
    # 初始化 JavaScript 环境
    ctx = MiniRacer()
    ctx.eval(js_code_for_unzip_xml)
    # 调用 JavaScript 函数进行解码
    decoded_data = ctx.call("__WX_MER_DECODE__.decodeReportData.toXML", csv_data)
    return decoded_data


def get_base_url() -> str:
    """
    获取 base url
    """
    return os.getenv("WXMS_BASE_URL") or "http://127.0.0.1:80"


def get_sandbox_js_api_runtime_evaluate_params(js_method: str) -> dict:
    """
    获取调用沙箱 js api 的 runtime_evaluate 参数

    :param js_method: js api 的方法名和入参，例如 getDomXml()
    """
    return {
        "method": "Runtime.evaluate",
        "params": {
            "expression": f"{js_code_xbar}window.__WX_WM_SANDBOX__.{js_method}",
            "returnByValue": True,  # 需要返回值
        },
    }


def image_save(
    path: str | None, image_bytes: bytes
) -> tuple[tuple[int, int], tuple[int, int]]:
    """
    保存图片，resize 的步骤如下（长边短边并不是指长度长，而是指相较于固定尺寸比例的长边短边）：
    一，图片长边对齐进行 resize，从而使得宽等于 APPLET_FIXED_WIDTH 或高等于 APPLET_FIXED_HEIGHT
    二，图片左上角顶点对齐
    三，短边 padding 留白

    :param path: 保存图片的路径
    :param image_bytes: 图片的字节数据
    :return: 原始图片对应的宽高，步骤一 resize 后的宽高
    """
    image = Image.open(io.BytesIO(image_bytes))
    logging.info(f"screenshot captured size: {image.size}")  # pylint: disable=W1203
    # 原来图片的尺寸
    real_size = image.size
    enable_new_resize = os.getenv("WXMS_FEATURE_ENABLE_NEW_RESIZE")
    if real_size[0] * APPLET_FIXED_HEIGHT >= APPLET_FIXED_WIDTH * real_size[1] or (
        not enable_new_resize
    ):
        # 下方留白
        resized_size = (
            APPLET_FIXED_WIDTH,
            round(real_size[1] * APPLET_FIXED_WIDTH / real_size[0]),
        )
    else:
        # 右方留白
        resized_size = (
            round(real_size[0] * APPLET_FIXED_HEIGHT / real_size[1]),
            APPLET_FIXED_HEIGHT,
        )
    logging.info(  # pylint: disable=W1203
        f"screenshot resize from {real_size} to: {resized_size}"
    )
    # 保存图片
    if path is not None:
        # resize 图片
        image = image.resize(
            resized_size,
            Image.Resampling.LANCZOS,
        )
        if enable_new_resize:
            # 左上角顶点对齐，短边 padding 留白
            image = ImageOps.pad(
                image,
                (APPLET_FIXED_WIDTH, APPLET_FIXED_HEIGHT),
                method=Image.Resampling.LANCZOS,
                color="white",
                centering=(0, 0),
            )
        if image.mode == "RGBA":
            # 如果图像是 RGBA 模式，转换为 RGB 后再保存为 JPEG
            image = image.convert("RGB")
        image.save(path)
        logging.info(f"Screenshot saved to {path}")  # pylint: disable=W1203
    return real_size, resized_size


def translate_coordinate_from_fixed_to_real(
    coordinate_fixed: tuple[int, int],
    real_size: tuple[int, int],
    resized_size: tuple[int, int],
) -> tuple[int, int]:
    """
    截图的时候会将设备的屏幕尺寸固定缩放至 APPLET_FIXED_WIDTH * APPLET_FIXED_HEIGHT 的框中
    从而导致视觉模型推理结果的坐标不是设备上的真实坐标
    所以如果是固定尺寸坐标系上的坐标，则需要转换成设备上的真实坐标

    :param coordinate_fixed: 固定尺寸坐标系上的坐标
    :param real_size: 设备上的真实宽高
    :param resized_size: resize 后的宽高（不含 padding 的留白）
    :return: 设备上的真实坐标
    """
    res_x = int(coordinate_fixed[0] * real_size[0] / resized_size[0])
    res_y = int(coordinate_fixed[1] * real_size[1] / resized_size[1])
    # 检查点击是否在有效区域内
    if res_x < 0:
        res_x = 0
    if res_x > real_size[0]:
        res_x = real_size[0]
    if res_y < 0:
        res_y = 0
    if res_y > real_size[1]:
        res_y = real_size[1]
    return (res_x, res_y)
