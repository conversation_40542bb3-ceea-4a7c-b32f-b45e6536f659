
window.__WX_WM_SANDBOX_VERSION__ = '0.3.0';
window.__WX_WM_SANDBOX_BUILD_TIME__ = '2025/6/4 16:40:59';
"use strict";(()=>{var Nn=Object.create;var Ae=Object.defineProperty;var xn=Object.getOwnPropertyDescriptor;var Cn=Object.getOwnPropertyNames;var Mn=Object.getPrototypeOf,Sn=Object.prototype.hasOwnProperty;var Ln=(o,i)=>()=>(i||o((i={exports:{}}).exports,i),i.exports);var Rn=(o,i,c,a)=>{if(i&&typeof i=="object"||typeof i=="function")for(let l of Cn(i))!Sn.call(o,l)&&l!==c&&Ae(o,l,{get:()=>i[l],enumerable:!(a=xn(i,l))||a.enumerable});return o};var On=(o,i,c)=>(c=o!=null?Nn(Mn(o)):{},Rn(i||!o||!o.__esModule?Ae(c,"default",{value:o,enumerable:!0}):c,o));var $e=Ln((bi,Vt)=>{"use strict";(function(o,i,c,a){"use strict";var l=["","webkit","Moz","MS","ms","o"],v=i.createElement("div"),p="function",y=Math.round,E=Math.abs,w=Date.now;function R(t,e,n){return setTimeout(tt(t,n),e)}function H(t,e,n){return Array.isArray(t)?($(t,n[e],n),!0):!1}function $(t,e,n){var s;if(t)if(t.forEach)t.forEach(e,n);else if(t.length!==a)for(s=0;s<t.length;)e.call(n,t[s],s,t),s++;else for(s in t)t.hasOwnProperty(s)&&e.call(n,t[s],s,t)}function ht(t,e,n){var s="DEPRECATED METHOD: "+e+`
`+n+` AT 
`;return function(){var u=new Error("get-stack-trace"),m=u&&u.stack?u.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",b=o.console&&(o.console.warn||o.console.log);return b&&b.call(o.console,s,m),t.apply(this,arguments)}}var f;typeof Object.assign!="function"?f=function(e){if(e===a||e===null)throw new TypeError("Cannot convert undefined or null to object");for(var n=Object(e),s=1;s<arguments.length;s++){var u=arguments[s];if(u!==a&&u!==null)for(var m in u)u.hasOwnProperty(m)&&(n[m]=u[m])}return n}:f=Object.assign;var Yt=ht(function(e,n,s){for(var u=Object.keys(n),m=0;m<u.length;)(!s||s&&e[u[m]]===a)&&(e[u[m]]=n[u[m]]),m++;return e},"extend","Use `assign`."),Tt=ht(function(e,n){return Yt(e,n,!0)},"merge","Use `assign`.");function L(t,e,n){var s=e.prototype,u;u=t.prototype=Object.create(s),u.constructor=t,u._super=s,n&&f(u,n)}function tt(t,e){return function(){return t.apply(e,arguments)}}function gt(t,e){return typeof t==p?t.apply(e&&e[0]||a,e):t}function at(t,e){return t===a?e:t}function ct(t,e,n){$(vt(e),function(s){t.addEventListener(s,n,!1)})}function ft(t,e,n){$(vt(e),function(s){t.removeEventListener(s,n,!1)})}function Mt(t,e){for(;t;){if(t==e)return!0;t=t.parentNode}return!1}function et(t,e){return t.indexOf(e)>-1}function vt(t){return t.trim().split(/\s+/g)}function lt(t,e,n){if(t.indexOf&&!n)return t.indexOf(e);for(var s=0;s<t.length;){if(n&&t[s][n]==e||!n&&t[s]===e)return s;s++}return-1}function pt(t){return Array.prototype.slice.call(t,0)}function jt(t,e,n){for(var s=[],u=[],m=0;m<t.length;){var b=e?t[m][e]:t[m];lt(u,b)<0&&s.push(t[m]),u[m]=b,m++}return n&&(e?s=s.sort(function(B,W){return B[e]>W[e]}):s=s.sort()),s}function bt(t,e){for(var n,s,u=e[0].toUpperCase()+e.slice(1),m=0;m<l.length;){if(n=l[m],s=n?n+u:e,s in t)return s;m++}return a}var Gt=1;function me(){return Gt++}function nt(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||o}var St=/mobile|tablet|ip(ad|hone|od)|android/i,r="ontouchstart"in o,h=bt(o,"PointerEvent")!==a,g=r&&St.test(navigator.userAgent),d="touch",N="pen",x="mouse",T="kinect",M=25,_=1,C=2,I=4,S=8,F=1,O=2,V=4,Y=8,J=16,A=O|V,G=Y|J,X=A|G,it=["x","y"],z=["clientX","clientY"];function P(t,e){var n=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(s){gt(t.options.enable,[t])&&n.handler(s)},this.init()}P.prototype={handler:function(){},init:function(){this.evEl&&ct(this.element,this.evEl,this.domHandler),this.evTarget&&ct(this.target,this.evTarget,this.domHandler),this.evWin&&ct(nt(this.element),this.evWin,this.domHandler)},destroy:function(){this.evEl&&ft(this.element,this.evEl,this.domHandler),this.evTarget&&ft(this.target,this.evTarget,this.domHandler),this.evWin&&ft(nt(this.element),this.evWin,this.domHandler)}};function Lt(t){var e,n=t.options.inputClass;return n?e=n:h?e=qt:g?e=Dt:r?e=Jt:e=Pt,new e(t,Rt)}function Rt(t,e,n){var s=n.pointers.length,u=n.changedPointers.length,m=e&_&&s-u===0,b=e&(I|S)&&s-u===0;n.isFirst=!!m,n.isFinal=!!b,m&&(t.session={}),n.eventType=e,Ot(t,n),t.emit("hammer.input",n),t.recognize(n),t.session.prevInput=n}function Ot(t,e){var n=t.session,s=e.pointers,u=s.length;n.firstInput||(n.firstInput=ge(e)),u>1&&!n.firstMultiple?n.firstMultiple=ge(e):u===1&&(n.firstMultiple=!1);var m=n.firstInput,b=n.firstMultiple,D=b?b.center:m.center,B=e.center=ve(s);e.timeStamp=w(),e.deltaTime=e.timeStamp-m.timeStamp,e.angle=zt(D,B),e.distance=At(D,B),tn(n,e),e.offsetDirection=ye(e.deltaX,e.deltaY);var W=Ee(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=W.x,e.overallVelocityY=W.y,e.overallVelocity=E(W.x)>E(W.y)?W.x:W.y,e.scale=b?on(b.pointers,s):1,e.rotation=b?nn(b.pointers,s):0,e.maxPointers=n.prevInput?e.pointers.length>n.prevInput.maxPointers?e.pointers.length:n.prevInput.maxPointers:e.pointers.length,en(n,e);var Q=t.element;Mt(e.srcEvent.target,Q)&&(Q=e.srcEvent.target),e.target=Q}function tn(t,e){var n=e.center,s=t.offsetDelta||{},u=t.prevDelta||{},m=t.prevInput||{};(e.eventType===_||m.eventType===I)&&(u=t.prevDelta={x:m.deltaX||0,y:m.deltaY||0},s=t.offsetDelta={x:n.x,y:n.y}),e.deltaX=u.x+(n.x-s.x),e.deltaY=u.y+(n.y-s.y)}function en(t,e){var n=t.lastInterval||e,s=e.timeStamp-n.timeStamp,u,m,b,D;if(e.eventType!=S&&(s>M||n.velocity===a)){var B=e.deltaX-n.deltaX,W=e.deltaY-n.deltaY,Q=Ee(s,B,W);m=Q.x,b=Q.y,u=E(Q.x)>E(Q.y)?Q.x:Q.y,D=ye(B,W),t.lastInterval=e}else u=n.velocity,m=n.velocityX,b=n.velocityY,D=n.direction;e.velocity=u,e.velocityX=m,e.velocityY=b,e.direction=D}function ge(t){for(var e=[],n=0;n<t.pointers.length;)e[n]={clientX:y(t.pointers[n].clientX),clientY:y(t.pointers[n].clientY)},n++;return{timeStamp:w(),pointers:e,center:ve(e),deltaX:t.deltaX,deltaY:t.deltaY}}function ve(t){var e=t.length;if(e===1)return{x:y(t[0].clientX),y:y(t[0].clientY)};for(var n=0,s=0,u=0;u<e;)n+=t[u].clientX,s+=t[u].clientY,u++;return{x:y(n/e),y:y(s/e)}}function Ee(t,e,n){return{x:e/t||0,y:n/t||0}}function ye(t,e){return t===e?F:E(t)>=E(e)?t<0?O:V:e<0?Y:J}function At(t,e,n){n||(n=it);var s=e[n[0]]-t[n[0]],u=e[n[1]]-t[n[1]];return Math.sqrt(s*s+u*u)}function zt(t,e,n){n||(n=it);var s=e[n[0]]-t[n[0]],u=e[n[1]]-t[n[1]];return Math.atan2(u,s)*180/Math.PI}function nn(t,e){return zt(e[1],e[0],z)+zt(t[1],t[0],z)}function on(t,e){return At(e[0],e[1],z)/At(t[0],t[1],z)}var rn={mousedown:_,mousemove:C,mouseup:I},sn="mousedown",an="mousemove mouseup";function Pt(){this.evEl=sn,this.evWin=an,this.pressed=!1,P.apply(this,arguments)}L(Pt,P,{handler:function(e){var n=rn[e.type];n&_&&e.button===0&&(this.pressed=!0),n&C&&e.which!==1&&(n=I),this.pressed&&(n&I&&(this.pressed=!1),this.callback(this.manager,n,{pointers:[e],changedPointers:[e],pointerType:x,srcEvent:e}))}});var cn={pointerdown:_,pointermove:C,pointerup:I,pointercancel:S,pointerout:S},ln={2:d,3:N,4:x,5:T},Te="pointerdown",be="pointermove pointerup pointercancel";o.MSPointerEvent&&!o.PointerEvent&&(Te="MSPointerDown",be="MSPointerMove MSPointerUp MSPointerCancel");function qt(){this.evEl=Te,this.evWin=be,P.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}L(qt,P,{handler:function(e){var n=this.store,s=!1,u=e.type.toLowerCase().replace("ms",""),m=cn[u],b=ln[e.pointerType]||e.pointerType,D=b==d,B=lt(n,e.pointerId,"pointerId");m&_&&(e.button===0||D)?B<0&&(n.push(e),B=n.length-1):m&(I|S)&&(s=!0),!(B<0)&&(n[B]=e,this.callback(this.manager,m,{pointers:n,changedPointers:[e],pointerType:b,srcEvent:e}),s&&n.splice(B,1))}});var un={touchstart:_,touchmove:C,touchend:I,touchcancel:S},hn="touchstart",fn="touchstart touchmove touchend touchcancel";function _e(){this.evTarget=hn,this.evWin=fn,this.started=!1,P.apply(this,arguments)}L(_e,P,{handler:function(e){var n=un[e.type];if(n===_&&(this.started=!0),!!this.started){var s=pn.call(this,e,n);n&(I|S)&&s[0].length-s[1].length===0&&(this.started=!1),this.callback(this.manager,n,{pointers:s[0],changedPointers:s[1],pointerType:d,srcEvent:e})}}});function pn(t,e){var n=pt(t.touches),s=pt(t.changedTouches);return e&(I|S)&&(n=jt(n.concat(s),"identifier",!0)),[n,s]}var dn={touchstart:_,touchmove:C,touchend:I,touchcancel:S},mn="touchstart touchmove touchend touchcancel";function Dt(){this.evTarget=mn,this.targetIds={},P.apply(this,arguments)}L(Dt,P,{handler:function(e){var n=dn[e.type],s=gn.call(this,e,n);s&&this.callback(this.manager,n,{pointers:s[0],changedPointers:s[1],pointerType:d,srcEvent:e})}});function gn(t,e){var n=pt(t.touches),s=this.targetIds;if(e&(_|C)&&n.length===1)return s[n[0].identifier]=!0,[n,n];var u,m,b=pt(t.changedTouches),D=[],B=this.target;if(m=n.filter(function(W){return Mt(W.target,B)}),e===_)for(u=0;u<m.length;)s[m[u].identifier]=!0,u++;for(u=0;u<b.length;)s[b[u].identifier]&&D.push(b[u]),e&(I|S)&&delete s[b[u].identifier],u++;if(D.length)return[jt(m.concat(D),"identifier",!0),D]}var vn=2500,Ie=25;function Jt(){P.apply(this,arguments);var t=tt(this.handler,this);this.touch=new Dt(this.manager,t),this.mouse=new Pt(this.manager,t),this.primaryTouch=null,this.lastTouches=[]}L(Jt,P,{handler:function(e,n,s){var u=s.pointerType==d,m=s.pointerType==x;if(!(m&&s.sourceCapabilities&&s.sourceCapabilities.firesTouchEvents)){if(u)En.call(this,n,s);else if(m&&yn.call(this,s))return;this.callback(e,n,s)}},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});function En(t,e){t&_?(this.primaryTouch=e.changedPointers[0].identifier,we.call(this,e)):t&(I|S)&&we.call(this,e)}function we(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var n={x:e.clientX,y:e.clientY};this.lastTouches.push(n);var s=this.lastTouches,u=function(){var m=s.indexOf(n);m>-1&&s.splice(m,1)};setTimeout(u,vn)}}function yn(t){for(var e=t.srcEvent.clientX,n=t.srcEvent.clientY,s=0;s<this.lastTouches.length;s++){var u=this.lastTouches[s],m=Math.abs(e-u.x),b=Math.abs(n-u.y);if(m<=Ie&&b<=Ie)return!0}return!1}var Ne=bt(v.style,"touchAction"),xe=Ne!==a,Ce="compute",Me="auto",Zt="manipulation",dt="none",_t="pan-x",It="pan-y",kt=bn();function Qt(t,e){this.manager=t,this.set(e)}Qt.prototype={set:function(t){t==Ce&&(t=this.compute()),xe&&this.manager.element.style&&kt[t]&&(this.manager.element.style[Ne]=t),this.actions=t.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)},compute:function(){var t=[];return $(this.manager.recognizers,function(e){gt(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))}),Tn(t.join(" "))},preventDefaults:function(t){var e=t.srcEvent,n=t.offsetDirection;if(this.manager.session.prevented){e.preventDefault();return}var s=this.actions,u=et(s,dt)&&!kt[dt],m=et(s,It)&&!kt[It],b=et(s,_t)&&!kt[_t];if(u){var D=t.pointers.length===1,B=t.distance<2,W=t.deltaTime<250;if(D&&B&&W)return}if(!(b&&m)&&(u||m&&n&A||b&&n&G))return this.preventSrc(e)},preventSrc:function(t){this.manager.session.prevented=!0,t.preventDefault()}};function Tn(t){if(et(t,dt))return dt;var e=et(t,_t),n=et(t,It);return e&&n?dt:e||n?e?_t:It:et(t,Zt)?Zt:Me}function bn(){if(!xe)return!1;var t={},e=o.CSS&&o.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach(function(n){t[n]=e?o.CSS.supports("touch-action",n):!0}),t}var Ht=1,j=2,Et=4,ut=8,ot=ut,wt=16,Z=32;function rt(t){this.options=f({},this.defaults,t||{}),this.id=me(),this.manager=null,this.options.enable=at(this.options.enable,!0),this.state=Ht,this.simultaneous={},this.requireFail=[]}rt.prototype={defaults:{},set:function(t){return f(this.options,t),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(t){if(H(t,"recognizeWith",this))return this;var e=this.simultaneous;return t=Xt(t,this),e[t.id]||(e[t.id]=t,t.recognizeWith(this)),this},dropRecognizeWith:function(t){return H(t,"dropRecognizeWith",this)?this:(t=Xt(t,this),delete this.simultaneous[t.id],this)},requireFailure:function(t){if(H(t,"requireFailure",this))return this;var e=this.requireFail;return t=Xt(t,this),lt(e,t)===-1&&(e.push(t),t.requireFailure(this)),this},dropRequireFailure:function(t){if(H(t,"dropRequireFailure",this))return this;t=Xt(t,this);var e=lt(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(t){return!!this.simultaneous[t.id]},emit:function(t){var e=this,n=this.state;function s(u){e.manager.emit(u,t)}n<ut&&s(e.options.event+Se(n)),s(e.options.event),t.additionalEvent&&s(t.additionalEvent),n>=ut&&s(e.options.event+Se(n))},tryEmit:function(t){if(this.canEmit())return this.emit(t);this.state=Z},canEmit:function(){for(var t=0;t<this.requireFail.length;){if(!(this.requireFail[t].state&(Z|Ht)))return!1;t++}return!0},recognize:function(t){var e=f({},t);if(!gt(this.options.enable,[this,e])){this.reset(),this.state=Z;return}this.state&(ot|wt|Z)&&(this.state=Ht),this.state=this.process(e),this.state&(j|Et|ut|wt)&&this.tryEmit(e)},process:function(t){},getTouchAction:function(){},reset:function(){}};function Se(t){return t&wt?"cancel":t&ut?"end":t&Et?"move":t&j?"start":""}function Le(t){return t==J?"down":t==Y?"up":t==O?"left":t==V?"right":""}function Xt(t,e){var n=e.manager;return n?n.get(t):t}function q(){rt.apply(this,arguments)}L(q,rt,{defaults:{pointers:1},attrTest:function(t){var e=this.options.pointers;return e===0||t.pointers.length===e},process:function(t){var e=this.state,n=t.eventType,s=e&(j|Et),u=this.attrTest(t);return s&&(n&S||!u)?e|wt:s||u?n&I?e|ut:e&j?e|Et:j:Z}});function Bt(){q.apply(this,arguments),this.pX=null,this.pY=null}L(Bt,q,{defaults:{event:"pan",threshold:10,pointers:1,direction:X},getTouchAction:function(){var t=this.options.direction,e=[];return t&A&&e.push(It),t&G&&e.push(_t),e},directionTest:function(t){var e=this.options,n=!0,s=t.distance,u=t.direction,m=t.deltaX,b=t.deltaY;return u&e.direction||(e.direction&A?(u=m===0?F:m<0?O:V,n=m!=this.pX,s=Math.abs(t.deltaX)):(u=b===0?F:b<0?Y:J,n=b!=this.pY,s=Math.abs(t.deltaY))),t.direction=u,n&&s>e.threshold&&u&e.direction},attrTest:function(t){return q.prototype.attrTest.call(this,t)&&(this.state&j||!(this.state&j)&&this.directionTest(t))},emit:function(t){this.pX=t.deltaX,this.pY=t.deltaY;var e=Le(t.direction);e&&(t.additionalEvent=this.options.event+e),this._super.emit.call(this,t)}});function Kt(){q.apply(this,arguments)}L(Kt,q,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return[dt]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.scale-1)>this.options.threshold||this.state&j)},emit:function(t){if(t.scale!==1){var e=t.scale<1?"in":"out";t.additionalEvent=this.options.event+e}this._super.emit.call(this,t)}});function te(){rt.apply(this,arguments),this._timer=null,this._input=null}L(te,rt,{defaults:{event:"press",pointers:1,time:251,threshold:9},getTouchAction:function(){return[Me]},process:function(t){var e=this.options,n=t.pointers.length===e.pointers,s=t.distance<e.threshold,u=t.deltaTime>e.time;if(this._input=t,!s||!n||t.eventType&(I|S)&&!u)this.reset();else if(t.eventType&_)this.reset(),this._timer=R(function(){this.state=ot,this.tryEmit()},e.time,this);else if(t.eventType&I)return ot;return Z},reset:function(){clearTimeout(this._timer)},emit:function(t){this.state===ot&&(t&&t.eventType&I?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=w(),this.manager.emit(this.options.event,this._input)))}});function ee(){q.apply(this,arguments)}L(ee,q,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return[dt]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.rotation)>this.options.threshold||this.state&j)}});function ne(){q.apply(this,arguments)}L(ne,q,{defaults:{event:"swipe",threshold:10,velocity:.3,direction:A|G,pointers:1},getTouchAction:function(){return Bt.prototype.getTouchAction.call(this)},attrTest:function(t){var e=this.options.direction,n;return e&(A|G)?n=t.overallVelocity:e&A?n=t.overallVelocityX:e&G&&(n=t.overallVelocityY),this._super.attrTest.call(this,t)&&e&t.offsetDirection&&t.distance>this.options.threshold&&t.maxPointers==this.options.pointers&&E(n)>this.options.velocity&&t.eventType&I},emit:function(t){var e=Le(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)}});function Ft(){rt.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}L(Ft,rt,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},getTouchAction:function(){return[Zt]},process:function(t){var e=this.options,n=t.pointers.length===e.pointers,s=t.distance<e.threshold,u=t.deltaTime<e.time;if(this.reset(),t.eventType&_&&this.count===0)return this.failTimeout();if(s&&u&&n){if(t.eventType!=I)return this.failTimeout();var m=this.pTime?t.timeStamp-this.pTime<e.interval:!0,b=!this.pCenter||At(this.pCenter,t.center)<e.posThreshold;this.pTime=t.timeStamp,this.pCenter=t.center,!b||!m?this.count=1:this.count+=1,this._input=t;var D=this.count%e.taps;if(D===0)return this.hasRequireFailures()?(this._timer=R(function(){this.state=ot,this.tryEmit()},e.interval,this),j):ot}return Z},failTimeout:function(){return this._timer=R(function(){this.state=Z},this.options.interval,this),Z},reset:function(){clearTimeout(this._timer)},emit:function(){this.state==ot&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}});function st(t,e){return e=e||{},e.recognizers=at(e.recognizers,st.defaults.preset),new ie(t,e)}st.VERSION="2.0.7",st.defaults={domEvents:!1,touchAction:Ce,enable:!0,inputTarget:null,inputClass:null,preset:[[ee,{enable:!1}],[Kt,{enable:!1},["rotate"]],[ne,{direction:A}],[Bt,{direction:A},["swipe"]],[Ft],[Ft,{event:"doubletap",taps:2},["tap"]],[te]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}};var _n=1,Re=2;function ie(t,e){this.options=f({},st.defaults,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=Lt(this),this.touchAction=new Qt(this,this.options.touchAction),Oe(this,!0),$(this.options.recognizers,function(n){var s=this.add(new n[0](n[1]));n[2]&&s.recognizeWith(n[2]),n[3]&&s.requireFailure(n[3])},this)}ie.prototype={set:function(t){return f(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},stop:function(t){this.session.stopped=t?Re:_n},recognize:function(t){var e=this.session;if(!e.stopped){this.touchAction.preventDefaults(t);var n,s=this.recognizers,u=e.curRecognizer;(!u||u&&u.state&ot)&&(u=e.curRecognizer=null);for(var m=0;m<s.length;)n=s[m],e.stopped!==Re&&(!u||n==u||n.canRecognizeWith(u))?n.recognize(t):n.reset(),!u&&n.state&(j|Et|ut)&&(u=e.curRecognizer=n),m++}},get:function(t){if(t instanceof rt)return t;for(var e=this.recognizers,n=0;n<e.length;n++)if(e[n].options.event==t)return e[n];return null},add:function(t){if(H(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},remove:function(t){if(H(t,"remove",this))return this;if(t=this.get(t),t){var e=this.recognizers,n=lt(e,t);n!==-1&&(e.splice(n,1),this.touchAction.update())}return this},on:function(t,e){if(t!==a&&e!==a){var n=this.handlers;return $(vt(t),function(s){n[s]=n[s]||[],n[s].push(e)}),this}},off:function(t,e){if(t!==a){var n=this.handlers;return $(vt(t),function(s){e?n[s]&&n[s].splice(lt(n[s],e),1):delete n[s]}),this}},emit:function(t,e){this.options.domEvents&&In(t,e);var n=this.handlers[t]&&this.handlers[t].slice();if(!(!n||!n.length)){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var s=0;s<n.length;)n[s](e),s++}},destroy:function(){this.element&&Oe(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null}};function Oe(t,e){var n=t.element;if(n.style){var s;$(t.options.cssProps,function(u,m){s=bt(n.style,m),e?(t.oldCssProps[s]=n.style[s],n.style[s]=u):n.style[s]=t.oldCssProps[s]||""}),e||(t.oldCssProps={})}}function In(t,e){var n=i.createEvent("Event");n.initEvent(t,!0,!0),n.gesture=e,e.target.dispatchEvent(n)}f(st,{INPUT_START:_,INPUT_MOVE:C,INPUT_END:I,INPUT_CANCEL:S,STATE_POSSIBLE:Ht,STATE_BEGAN:j,STATE_CHANGED:Et,STATE_ENDED:ut,STATE_RECOGNIZED:ot,STATE_CANCELLED:wt,STATE_FAILED:Z,DIRECTION_NONE:F,DIRECTION_LEFT:O,DIRECTION_RIGHT:V,DIRECTION_UP:Y,DIRECTION_DOWN:J,DIRECTION_HORIZONTAL:A,DIRECTION_VERTICAL:G,DIRECTION_ALL:X,Manager:ie,Input:P,TouchAction:Qt,TouchInput:Dt,MouseInput:Pt,PointerEventInput:qt,TouchMouseInput:Jt,SingleTouchInput:_e,Recognizer:rt,AttrRecognizer:q,Tap:Ft,Pan:Bt,Swipe:ne,Pinch:Kt,Rotate:ee,Press:te,on:ct,off:ft,each:$,merge:Tt,extend:Yt,assign:f,inherit:L,bindFn:tt,prefixed:bt});var wn=typeof o<"u"?o:typeof self<"u"?self:{};wn.Hammer=st,typeof define=="function"&&define.amd?define(function(){return st}):typeof Vt<"u"&&Vt.exports?Vt.exports=st:o[c]=st})(window,document,"Hammer")});var U={silent:Number.NEGATIVE_INFINITY,fatal:0,error:0,warn:1,log:2,info:3,success:3,fail:3,ready:3,start:3,box:3,debug:4,trace:5,verbose:Number.POSITIVE_INFINITY},ae={silent:{level:-1},fatal:{level:U.fatal},error:{level:U.error},warn:{level:U.warn},log:{level:U.log},info:{level:U.info},success:{level:U.success},fail:{level:U.fail},ready:{level:U.info},start:{level:U.info},box:{level:U.info},debug:{level:U.debug},trace:{level:U.trace},verbose:{level:U.verbose}};function oe(o){if(o===null||typeof o!="object")return!1;let i=Object.getPrototypeOf(o);return i!==null&&i!==Object.prototype&&Object.getPrototypeOf(i)!==null||Symbol.iterator in o?!1:Symbol.toStringTag in o?Object.prototype.toString.call(o)==="[object Module]":!0}function ce(o,i,c=".",a){if(!oe(i))return ce(o,{},c,a);let l=Object.assign({},i);for(let v in o){if(v==="__proto__"||v==="constructor")continue;let p=o[v];p!=null&&(a&&a(l,v,p,c)||(Array.isArray(p)&&Array.isArray(l[v])?l[v]=[...p,...l[v]]:oe(p)&&oe(l[v])?l[v]=ce(p,l[v],(c?`${c}.`:"")+v.toString(),a):l[v]=p))}return l}function An(o){return(...i)=>i.reduce((c,a)=>ce(c,a,"",o),{})}var Pn=An();function Dn(o){return Object.prototype.toString.call(o)==="[object Object]"}function kn(o){return!(!Dn(o)||!o.message&&!o.args||o.stack)}var re=!1,Pe=[],k=class o{options;_lastLog;_mockFn;constructor(i={}){let c=i.types||ae;this.options=Pn({...i,defaults:{...i.defaults},level:se(i.level,c),reporters:[...i.reporters||[]]},{types:ae,throttle:1e3,throttleMin:5,formatOptions:{date:!0,colors:!1,compact:!0}});for(let a in c){let l={type:a,...this.options.defaults,...c[a]};this[a]=this._wrapLogFn(l),this[a].raw=this._wrapLogFn(l,!0)}this.options.mockFn&&this.mockTypes(),this._lastLog={}}get level(){return this.options.level}set level(i){this.options.level=se(i,this.options.types,this.options.level)}prompt(i,c){if(!this.options.prompt)throw new Error("prompt is not supported!");return this.options.prompt(i,c)}create(i){let c=new o({...this.options,...i});return this._mockFn&&c.mockTypes(this._mockFn),c}withDefaults(i){return this.create({...this.options,defaults:{...this.options.defaults,...i}})}withTag(i){return this.withDefaults({tag:this.options.defaults.tag?this.options.defaults.tag+":"+i:i})}addReporter(i){return this.options.reporters.push(i),this}removeReporter(i){if(i){let c=this.options.reporters.indexOf(i);if(c!==-1)return this.options.reporters.splice(c,1)}else this.options.reporters.splice(0);return this}setReporters(i){return this.options.reporters=Array.isArray(i)?i:[i],this}wrapAll(){this.wrapConsole(),this.wrapStd()}restoreAll(){this.restoreConsole(),this.restoreStd()}wrapConsole(){for(let i in this.options.types)console["__"+i]||(console["__"+i]=console[i]),console[i]=this[i].raw}restoreConsole(){for(let i in this.options.types)console["__"+i]&&(console[i]=console["__"+i],delete console["__"+i])}wrapStd(){this._wrapStream(this.options.stdout,"log"),this._wrapStream(this.options.stderr,"log")}_wrapStream(i,c){i&&(i.__write||(i.__write=i.write),i.write=a=>{this[c].raw(String(a).trim())})}restoreStd(){this._restoreStream(this.options.stdout),this._restoreStream(this.options.stderr)}_restoreStream(i){i&&i.__write&&(i.write=i.__write,delete i.__write)}pauseLogs(){re=!0}resumeLogs(){re=!1;let i=Pe.splice(0);for(let c of i)c[0]._logFn(c[1],c[2])}mockTypes(i){let c=i||this.options.mockFn;if(this._mockFn=c,typeof c=="function")for(let a in this.options.types)this[a]=c(a,this.options.types[a])||this[a],this[a].raw=this[a]}_wrapLogFn(i,c){return(...a)=>{if(re){Pe.push([this,i,a,c]);return}return this._logFn(i,a,c)}}_logFn(i,c,a){if((i.level||0)>this.level)return!1;let l={date:new Date,args:[],...i,level:se(i.level,this.options.types)};!a&&c.length===1&&kn(c[0])?Object.assign(l,c[0]):l.args=[...c],l.message&&(l.args.unshift(l.message),delete l.message),l.additional&&(Array.isArray(l.additional)||(l.additional=l.additional.split(`
`)),l.args.push(`
`+l.additional.join(`
`)),delete l.additional),l.type=typeof l.type=="string"?l.type.toLowerCase():"log",l.tag=typeof l.tag=="string"?l.tag:"";let v=(y=!1)=>{let E=(this._lastLog.count||0)-this.options.throttleMin;if(this._lastLog.object&&E>0){let w=[...this._lastLog.object.args];E>1&&w.push(`(repeated ${E} times)`),this._log({...this._lastLog.object,args:w}),this._lastLog.count=1}y&&(this._lastLog.object=l,this._log(l))};clearTimeout(this._lastLog.timeout);let p=this._lastLog.time&&l.date?l.date.getTime()-this._lastLog.time.getTime():0;if(this._lastLog.time=l.date,p<this.options.throttle)try{let y=JSON.stringify([l.type,l.tag,l.args]),E=this._lastLog.serialized===y;if(this._lastLog.serialized=y,E&&(this._lastLog.count=(this._lastLog.count||0)+1,this._lastLog.count>this.options.throttleMin)){this._lastLog.timeout=setTimeout(v,this.options.throttle);return}}catch{}v(!0)}_log(i){for(let c of this.options.reporters)c.log(i,{options:this.options})}};function se(o,i={},c=3){return o===void 0?c:typeof o=="number"?o:i[o]&&i[o].level!==void 0?i[o].level:c}k.prototype.add=k.prototype.addReporter;k.prototype.remove=k.prototype.removeReporter;k.prototype.clear=k.prototype.removeReporter;k.prototype.withScope=k.prototype.withTag;k.prototype.mock=k.prototype.mockTypes;k.prototype.pause=k.prototype.pauseLogs;k.prototype.resume=k.prototype.resumeLogs;function De(o={}){return new k(o)}var le=class{options;defaultColor;levelColorMap;typeColorMap;constructor(i){this.options={...i},this.defaultColor="#7f8c8d",this.levelColorMap={0:"#c0392b",1:"#f39c12",3:"#00BCD4"},this.typeColorMap={success:"#2ecc71"}}_getLogFn(i){return i<1?console.__error||console.error:i===1?console.__warn||console.warn:console.__log||console.log}log(i){let c=this._getLogFn(i.level),a=i.type==="log"?"":i.type,l=i.tag||"",p=`
      background: ${this.typeColorMap[i.type]||this.levelColorMap[i.level]||this.defaultColor};
      border-radius: 0.5em;
      color: white;
      font-weight: bold;
      padding: 2px 0.5em;
    `,y=`%c${[l,a].filter(Boolean).join(":")}`;typeof i.args[0]=="string"?c(`${y}%c ${i.args[0]}`,p,"",...i.args.slice(1)):c(y,p,...i.args)}};function Hn(o={}){return De({reporters:o.reporters||[new le({})],prompt(c,a={}){return a.type==="confirm"?Promise.resolve(confirm(c)):Promise.resolve(prompt(c))},...o})}var Nt=Hn();function ke(o){let i={tagName:"root",attributes:{},children:[]};for(let c of o){if(typeof c.id>"u")continue;let a=c.xpath?.split("/").filter(p=>p.length>0);if(!a||a.length===0){console.log("\u8282\u70B9\u6CA1\u6709 xpath",c);continue}let l=i,v=c.textContent||"";for(let p=0;p<a.length;p++){let y=p===a.length-1,E=a[p];if(E.startsWith("@")){l.attributes=c.attributes||{};break}let w=l.children?.find(R=>R.tagName===E);w||(w={tagName:E,attributes:{},children:[]},l.children.push(w)),l=w,y&&["id","attributes","isVisible","isInteractive","wxEvents","textContent"].forEach(H=>{c[H]!==void 0&&(w[H]=c[H])})}}return i.children[0]}function xt(o){return{x:Math.round(o.x),y:Math.round(o.y),width:Math.round(o.width),height:Math.round(o.height)}}function Ct(o,i=!0){let c=[],a=o;for(;a&&a.nodeType===Node.ELEMENT_NODE&&!(i&&(a.parentNode instanceof ShadowRoot||a.parentNode instanceof HTMLIFrameElement));){let l=0,v=a.previousSibling;for(;v;)v.nodeType===Node.ELEMENT_NODE&&v.nodeName===a.nodeName&&l++,v=v.previousSibling;let p=a.nodeName.toLowerCase(),y=l>0?`[${l+1}]`:"";c.unshift(`${p}${y}`),a=a.parentNode}return c.join("/")}var Xn=new Set(["a","button","details","embed","input","menu","menuitem","object","select","textarea","canvas","summary","dialog","banner"]),He=new Set(["button-icon","dialog","button-text-icon-only","treeitem","alert","grid","progressbar","radio","checkbox","menuitem","option","switch","dropdown","scrollbar","combobox","a-button-text","button","region","textbox","tabpanel","tab","click","button-text","spinbutton","a-button-inner","link","menu","slider","listbox","a-dropdown-button","button-icon-only","searchbox","menuitemradio","tooltip","tree","menuitemcheckbox"]),Xe={currentIframe:null,touchStartInfo:{xpath:"",touches:[]}},Wt="wx-mer-highlight-container";function yt(o={doHighlightElements:!0,focusHighlightIndex:-1,viewportExpansion:0,debugMode:!1,recordRects:!1,skipIframe:!1}){let{doHighlightElements:i,focusHighlightIndex:c=-1,viewportExpansion:a=0,debugMode:l}=o,v=0,p=document.getElementById(Wt);p&&p.remove();let y={nodeProcessing:[],treeTraversal:[],highlighting:[],current:null},E=window.innerWidth/2,w=window.innerHeight/2,R=document.elementFromPoint(E,w);for(;R&&!["body","iframe"].includes(R?.tagName.toLowerCase());)R=R.parentElement;let H=R;Xe.currentIframe=H;function $(r){y[r]=y[r]||[],y[r].push(performance.now())}function ht(r){let h=y[r].pop();return performance.now()-h}let f=l?{buildDomTreeCalls:0,timings:{buildDomTree:0,highlightElement:0,isInteractiveElement:0,isElementVisible:0,isTopElement:0,isInExpandedViewport:0,isTextNodeVisible:0,getEffectiveScroll:0},cacheMetrics:{boundingRectCacheHits:0,boundingRectCacheMisses:0,computedStyleCacheHits:0,computedStyleCacheMisses:0,getBoundingClientRectTime:0,getComputedStyleTime:0,boundingRectHitRate:0,computedStyleHitRate:0,overallHitRate:0},nodeMetrics:{totalNodes:0,processedNodes:0,skippedNodes:0},buildDomTreeBreakdown:{totalTime:0,totalSelfTime:0,buildDomTreeCalls:0,domOperations:{getBoundingClientRect:0,getComputedStyle:0},domOperationCounts:{getBoundingClientRect:0,getComputedStyle:0}}}:null;function Yt(r){return l?function(...h){let g=performance.now(),d=r.apply(this,h),N=performance.now()-g;return d}:r}function Tt(r,h){if(!l)return r();let g=performance.now(),d=r(),N=performance.now()-g;return f&&h in f.buildDomTreeBreakdown.domOperations&&(f.buildDomTreeBreakdown.domOperations[h]+=N,f.buildDomTreeBreakdown.domOperationCounts[h]++),d}let L={boundingRects:new WeakMap,computedStyles:new WeakMap,clearCache:()=>{L.boundingRects=new WeakMap,L.computedStyles=new WeakMap}};function tt(r){if(!r)return null;if(L.boundingRects.has(r))return l&&f&&f.cacheMetrics.boundingRectCacheHits++,L.boundingRects.get(r);l&&f&&f.cacheMetrics.boundingRectCacheMisses++;let h;if(l){let g=performance.now();h=r.getBoundingClientRect();let d=performance.now()-g;f&&(f.buildDomTreeBreakdown.domOperations.getBoundingClientRect+=d,f.buildDomTreeBreakdown.domOperationCounts.getBoundingClientRect++)}else h=r.getBoundingClientRect();return h&&L.boundingRects.set(r,h),h}function gt(r){if(!r)return null;if(L.computedStyles.has(r))return l&&f&&f.cacheMetrics.computedStyleCacheHits++,L.computedStyles.get(r);l&&f&&f.cacheMetrics.computedStyleCacheMisses++;let h;if(l){let g=performance.now();h=window.getComputedStyle(r);let d=performance.now()-g;f&&(f.buildDomTreeBreakdown.domOperations.getComputedStyle+=d,f.buildDomTreeBreakdown.domOperationCounts.getComputedStyle++)}else h=window.getComputedStyle(r);return h&&L.computedStyles.set(r,h),h}let at={},ct={current:0};function ft(r,h,g=null){if(!r)return h;try{let d=document.getElementById(Wt);d||(d=document.createElement("div"),d.id=Wt,d.style.position="fixed",d.style.pointerEvents="none",d.style.top="0",d.style.left="0",d.style.width="100%",d.style.height="100%",d.style.zIndex="2147483647",document.body.appendChild(d));let N=Tt(()=>r.getBoundingClientRect(),"getBoundingClientRect");if(!N)return h;let x=["#FF0000","#00FF00","#0000FF","#FFA500","#800080","#008080","#FF69B4","#4B0082","#FF4500","#2E8B57","#DC143C","#4682B4"],T=h%x.length,M=x[T],_=`${M}1A`,C=document.createElement("div");C.style.position="fixed",C.style.border=`2px solid ${M}`,C.style.backgroundColor=_,C.style.pointerEvents="none",C.style.boxSizing="border-box";let I={x:0,y:0};if(g){let X=g.getBoundingClientRect();I.x=X.left,I.y=X.top}let S=N.top+I.y,F=N.left+I.x;C.style.top=`${S}px`,C.style.left=`${F}px`,C.style.width=`${N.width}px`,C.style.height=`${N.height}px`;let O=document.createElement("div");O.className="playwright-highlight-label",O.style.position="fixed",O.style.background=M,O.style.color="white",O.style.padding="1px 4px",O.style.borderRadius="4px",O.style.fontSize=`${Math.min(12,Math.max(8,N.height/2))}px`,O.textContent=h.toString();let V=20,Y=16,J=S+2,A=F+N.width-V-2;(N.width<V+4||N.height<Y+4)&&(J=S-Y-2,A=F+N.width-V),O.style.top=`${J}px`,O.style.left=`${A}px`,d.appendChild(C),d.appendChild(O);let G=()=>{let X=r.getBoundingClientRect(),it={x:0,y:0};if(g){let Ot=g.getBoundingClientRect();it.x=Ot.left,it.y=Ot.top}let z=X.top+it.y,P=X.left+it.x;C.style.top=`${z}px`,C.style.left=`${P}px`,C.style.width=`${X.width}px`,C.style.height=`${X.height}px`;let Lt=z+2,Rt=P+X.width-V-2;(X.width<V+4||X.height<Y+4)&&(Lt=z-Y-2,Rt=P+X.width-V),O.style.top=`${Lt}px`,O.style.left=`${Rt}px`};return window.addEventListener("scroll",G),window.addEventListener("resize",G),h+1}finally{ht("highlighting")}}function Mt(r){try{let h=document.createRange();h.selectNodeContents(r);let g=h.getBoundingClientRect();if(g.width===0||g.height===0)return!1;let d=!(g.bottom<-a||g.top>window.innerHeight+a||g.right<-a||g.left>window.innerWidth+a),N=r.parentElement;if(!N)return!1;try{return d&&N.checkVisibility({checkOpacity:!0,checkVisibilityCSS:!0})}catch{let T=window.getComputedStyle(N);return d&&T.display!=="none"&&T.visibility!=="hidden"&&T.opacity!=="0"}}catch(h){return console.warn("Error checking text node visibility:",h),!1}}function et(r){if(!r||!("tagName"in r))return!1;let h=new Set(["body","div","main","article","section","nav","header","footer","wx-tab-bar-wrapper"]),g=r.tagName.toLowerCase(),d=g.startsWith("wx-");return h.has(g)||d?!0:!new Set(["head","svg","script","style","link","meta","noscript","template"]).has(g)}function vt(r){let h=gt(r);return h.height==="auto"&&h.width==="auto"?h.visibility!=="hidden"&&h.display!=="none":r.offsetWidth>0&&r.offsetHeight>0&&h.visibility!=="hidden"&&h.display!=="none"}function lt(r){if(!r||r.nodeType!==Node.ELEMENT_NODE)return!1;let h=r.tagName.toLowerCase(),g=r.getAttribute("role"),d=r.getAttribute("aria-role"),N=r.getAttribute("tabindex");if(h==="html")return!1;let x=Object.keys(r.__wxElement?.__wxEvents||{}).filter(A=>!["error","load"].includes(A)&&!A.startsWith("_")),T=r.classList&&(r.classList.contains("address-input__container__input")||r.classList.contains("nav-btn")||r.classList.contains("pull-left"));if(r.classList&&(r.classList.contains("dropdown-toggle")||r.getAttribute("data-toggle")==="dropdown"||r.getAttribute("aria-haspopup")==="true")||T||Xn.has(h)||He.has(g||"")||He.has(d||"")||N!==null&&N!=="-1"&&r.parentElement?.tagName.toLowerCase()!=="body"||r.getAttribute("data-action")==="a-dropdown-select"||r.getAttribute("data-action")==="a-dropdown-button"||x.length>0||typeof r.closest=="function"&&r.closest('[id*="cookie"],[id*="consent"],[class*="cookie"],[class*="consent"],[id*="onetrust"]')&&(r.tagName.toLowerCase()==="button"||r.getAttribute("role")==="button"||r.classList&&r.classList.contains("button")||r.onclick||r.getAttribute("onclick")))return!0;let C=window.getComputedStyle(r),I=r.onclick!==null||r.getAttribute("onclick")!==null||r.hasAttribute("ng-click")||r.hasAttribute("@click")||r.hasAttribute("v-on:click");function S(A){try{return window.getEventListeners?.(A)||{}}catch{let X={},it=["click","mousedown","mouseup","touchstart","touchend","keydown","keyup","focus","blur"];for(let z of it){let P=A[`on${z}`];P&&(X[z]=[{listener:P,useCapture:!1}])}return X}}let F=S(r),O=F&&(F.click?.length>0||F.mousedown?.length>0||F.mouseup?.length>0||F.touchstart?.length>0||F.touchend?.length>0),V=r.hasAttribute("aria-expanded")||r.hasAttribute("aria-pressed")||r.hasAttribute("aria-selected")||r.hasAttribute("aria-checked"),Y=r.getAttribute("contenteditable")==="true"||r.isContentEditable||r.id==="tinymce"||r.classList.contains("mce-content-body")||r.tagName.toLowerCase()==="body"&&r.getAttribute("data-id")?.startsWith("mce_"),J=r.draggable||r.getAttribute("draggable")==="true";return V||I||O||J||Y}function pt(r){let h=!1;r.parentElement?.tagName==="wx-historylist"&&(h=!0);let g=tt(r);if(!(g.left<window.innerWidth&&g.right>0&&g.top<window.innerHeight&&g.bottom>0))return!1;let N=r.ownerDocument;if(N!==window.document&&N!==H?.contentDocument)return!1;let x=r.getRootNode();if(x instanceof ShadowRoot||x===r.ownerDocument){let _=g.left+g.width/2,C=g.top+g.height/2;try{let I=Tt(()=>x.elementFromPoint(_,C),"elementFromPoint");if(!I)return!1;h&&r.contains(R);let S=I;if(r.contains(I)||I.contains(r))return!0;for(;S&&S!==x;){if(S===r)return!0;S=S.parentElement}return!1}catch{return!0}}let T=g.left+g.width/2,M=g.top+g.height/2;try{let _=document.elementFromPoint(T,M);if(!_)return!1;let C=_;if(r.contains(_))return!0;for(;C&&C!==document.documentElement;){if(C===r)return!0;C=C.parentElement}return!1}catch{return!0}}function jt(r,h){if(h===-1)return!0;let g=tt(r);return!(g.bottom<-h||g.top>window.innerHeight+h||g.right<-h||g.left>window.innerWidth+h)}function bt(r){let h=r,g=0,d=0;return Tt(()=>{for(;h&&h!==document.documentElement;)(h.scrollLeft||h.scrollTop)&&(g+=h.scrollLeft,d+=h.scrollTop),h=h.parentElement;return g+=window.scrollX,d+=window.scrollY,{scrollX:g,scrollY:d}},"scrollOperations")}function Gt(r){if(!r||r.nodeType!==Node.ELEMENT_NODE)return!1;let h=r.tagName.toLowerCase();return new Set(["a","button","input","select","textarea","details","summary","wx-button","wx-input"]).has(h)?!0:r.hasAttribute("onclick")||r.hasAttribute("role")||r.hasAttribute("tabindex")||r.hasAttribute("aria-")||r.hasAttribute("data-action")||r.getAttribute("contenteditable")==="true"||r.getAttribute("event")}function me(r){return r.offsetWidth>0&&r.offsetHeight>0&&!r.hasAttribute("hidden")&&r.style.display!=="none"&&r.style.visibility!=="hidden"}function nt(r,h=null){if(l&&f.nodeMetrics.totalNodes++,!r||r.id===Wt)return l&&f.nodeMetrics.skippedNodes++,null;if(r===document){let x={tagName:"html",attributes:{},xpath:"html",children:[]};for(let M of r.childNodes){let _=nt(M,h);_&&x.children?.push(_)}let T=`${ct.current++}`;return at[T]=x,l&&f.nodeMetrics.processedNodes++,T}if(r.nodeType!==Node.ELEMENT_NODE&&r.nodeType!==Node.TEXT_NODE)return l&&f.nodeMetrics.skippedNodes++,null;if(r.nodeType===Node.TEXT_NODE){let x=r.textContent.trim();if(!x)return l&&f.nodeMetrics.skippedNodes++,null;let T=r.parentElement;if(!T||T.tagName.toLowerCase()==="script")return l&&f.nodeMetrics.skippedNodes++,null;let M=`${ct.current++}`;return at[M]={type:"TEXT_NODE",text:x,isVisible:Mt(r)},l&&f.nodeMetrics.processedNodes++,M}if(r.nodeType===Node.ELEMENT_NODE&&!et(r))return l&&f.nodeMetrics.skippedNodes++,null;if(a!==-1){let x=tt(r),T=gt(r),M=T&&(T.position==="fixed"||T.position==="sticky"),_=r.offsetWidth>0||r.offsetHeight>0||T&&T.height==="auto"&&T.width==="auto";if(!x||!M&&!_&&(x.bottom<-a||x.top>window.innerHeight+a||x.right<-a||x.left>window.innerWidth+a))return l&&f.nodeMetrics.skippedNodes++,null}let g=Ct(r,!0);h&&(g=`iframe/${g}`);let d={tagName:r.tagName.toLowerCase(),attributes:{},xpath:g,children:[]};if(Gt(r)||r.tagName.toLowerCase()==="iframe"||r.tagName.toLowerCase()==="body"){let x=r.getAttributeNames?.()||[],T=["style"];for(let M of x)T.includes(M)||(d.attributes[M]=r.getAttribute(M))}if(r.nodeType===Node.ELEMENT_NODE&&(d.tagName==="wx-tab-bar-wrapper"?(d.isVisible=!0,d.isTopElement=!0,d.isInteractive=!0):d.isVisible=vt(r),d.isVisible&&(o.recordRects&&(d.rect=xt(tt(r))),d.isTopElement=pt(r),d.isTopElement&&(d.isInteractive=lt(r),d.isInteractive&&(d.isInViewport=!0,d.highlightIndex=v++,d.wxEvents=Object.keys(r.__wxElement?.__wxEvents||{}),i&&(c>=0?c===d.highlightIndex&&ft(r,d.highlightIndex,h):ft(r,d.highlightIndex,h)))))),r.tagName){let x=r.tagName.toLowerCase();if(x==="iframe"&&!o.skipIframe)try{let T=r.contentDocument||r.contentWindow?.document;if(T)for(let M of T.childNodes){let _=nt(M,r);_&&d.children?.push(_)}}catch(T){console.warn("Unable to access iframe:",T)}else if(r.isContentEditable||r.getAttribute("contenteditable")==="true"||r.id==="tinymce"||r.classList.contains("mce-content-body")||x==="body"&&r.getAttribute("data-id")?.startsWith("mce_"))for(let T of r.childNodes){let M=nt(T,h);M&&d.children?.push(M)}else if(r.shadowRoot){d.shadowRoot=!0;for(let T of r.shadowRoot.childNodes){let M=nt(T,h);M&&d.children?.push(M)}}else for(let T of r.childNodes){let M=nt(T,h);M&&d.children?.push(M)}}if(d.tagName==="a"&&d.children?.length===0&&!d.attributes?.href)return l&&f.nodeMetrics.skippedNodes++,null;let N=`${ct.current++}`;return at[N]=d,l&&f.nodeMetrics.processedNodes++,N}let St=nt(document);if(L.clearCache(),l&&f){Object.keys(f.timings).forEach(g=>{f.timings[g]=f.timings[g]/1e3}),Object.keys(f.buildDomTreeBreakdown).forEach(g=>{typeof f.buildDomTreeBreakdown[g]=="number"&&(f.buildDomTreeBreakdown[g]=f.buildDomTreeBreakdown[g]/1e3)}),f.buildDomTreeBreakdown.buildDomTreeCalls>0&&(f.buildDomTreeBreakdown.averageTimePerNode=f.buildDomTreeBreakdown.totalTime/f.buildDomTreeBreakdown.buildDomTreeCalls),f.buildDomTreeBreakdown.timeInChildCalls=f.buildDomTreeBreakdown.totalTime-f.buildDomTreeBreakdown.totalSelfTime,Object.keys(f.buildDomTreeBreakdown.domOperations).forEach(g=>{let d=f.buildDomTreeBreakdown.domOperations[g],N=f.buildDomTreeBreakdown.domOperationCounts[g];N>0&&(f.buildDomTreeBreakdown.domOperations[`${g}Average`]=d/N)});let r=f.cacheMetrics.boundingRectCacheHits+f.cacheMetrics.boundingRectCacheMisses,h=f.cacheMetrics.computedStyleCacheHits+f.cacheMetrics.computedStyleCacheMisses;r>0&&(f.cacheMetrics.boundingRectHitRate=f.cacheMetrics.boundingRectCacheHits/r),h>0&&(f.cacheMetrics.computedStyleHitRate=f.cacheMetrics.computedStyleCacheHits/h),r+h>0&&(f.cacheMetrics.overallHitRate=(f.cacheMetrics.boundingRectCacheHits+f.cacheMetrics.computedStyleCacheHits)/(r+h))}return l?{rootId:St,map:at,perfMetrics:f,isTopElement:pt,getCachedBoundingRect:tt}:{rootId:St,map:at}}var Bn=["id","name","placeholder","data-event-opts"];function Be(o,i){let c=["iconfont","close","guanbi","active"];return o==="class"&&i?.[o]?c.some(a=>i[o].includes(a)):o.startsWith("aria-")||o.startsWith("data-")||Bn.includes(o)}var ue=window.__WX_MER__?.consoleLog||console.log,Fn=window.__WX_MER_DEBUG__??!1;Fn&&Wn("\u{1F41B} [DEBUG MODE]",window.__WX_MER_DEBUG__);function Wn(...o){ue(...o)}var K={debug:(...o)=>{(window.__WX_MER_DEBUG__??0)>=2&&ue("[MER] [DEBUG]",...o)},log:(...o)=>{(window.__WX_MER_DEBUG__??0)>=1&&ue("[MER]",...o)}},mi=/miniProgram/i.test(navigator.userAgent),Ve=!1;document.addEventListener("WeixinJSBridgeReady",()=>{Ve=window.__wxjs_environment==="miniprogram"});function Fe(o){return o=(o||navigator.userAgent).toLowerCase(),/android|adr/i.test(o)}function We(o){return o=(o||navigator.userAgent).toLowerCase(),/iphone|ipad|ipod/i.test(o)}var he={isAndroid:Fe(),isIOS:We(),desc:`${Fe()?"Android":We()?"iOS":"Unknown"} Mini Program`};var Un=On($e(),1);async function Ye(o,i){if(he.isAndroid){if(o==="report")return PageInfoReporterAndroid.report(i.action,i.type,i.csv,i.interactiveInfo||"",i.nativeComponentInfo||"",i.duration||0);if(o==="reportByNative")return PageInfoReporterAndroid.reportByNative(JSON.stringify(i));if(o==="recognizeImagesByNative")return PageInfoReporterAndroid.recognizeImagesByNative(i.infoStr);if(o==="getNativeComponentInfo")return PageInfoReporterAndroid.getNativeComponentInfo()}else{if(he.isIOS)return new Promise((c,a)=>{K.debug("callNativeFunction",o,i),window.pageInfoReporter.callMethod(o,i,l=>{c(l)})});K.log("callNativeFunction not implemented for this platform",o,i)}}async function Vn(o){try{let i=await Ye("recognizeImagesByNative",{infoStr:JSON.stringify(o.map(a=>({src:a.attributes?.src})))})||"[]",c=JSON.parse(i);c.length===o.length?o.forEach((a,l)=>{let v=c[l].label;v&&(a.attributes["aria-label"]=v)}):K.debug("\u{1F41B} \u8BC6\u522B\u56FE\u7247\u5931\u8D25\uFF0C\u957F\u5EA6\u4E0D\u5339\u914D",i)}catch(i){K.log("\u{1F41B} \u8BC6\u522B\u56FE\u7247\u5931\u8D25",i)}finally{K.debug("\u{1F41B} \u8BC6\u522B\u56FE\u7247",o.length,o,o.map(i=>i.attributes?.src)),o.forEach(i=>{i.attributes&&delete i.attributes.src})}}async function je(o,i={recordAll:!1}){let c=[],a=[],l=[];return Object.keys(o.map).forEach(v=>{let p=o.map[v];if(p.attributes||(p.attributes={}),p.children&&p.children.length&&p.children.some(E=>o.map[E].type==="TEXT_NODE"&&o.map[E].isVisible)&&(p.textContent=p.children.map(E=>o.map[E].text||o.map[E].el?.textContent).join(" "),delete p.children),p.id=v,i.recordAll&&p.xpath){a.push(p);return}if(p.isTopElement&&(p.isInViewport||typeof p.isInViewport>"u")){let y=p.isVisible&&!!p.textContent,E=p.isInteractive&&((p.wxEvents||[]).length>0||["button","a"].includes(p.tagName||"")),w=!1;p.tagName==="wx-image"&&p.attributes&&"src"in p.attributes&&(p.attributes["aria-label"]?delete p.attributes.src:l.push(p),w=!!(p.isVisible||p.isInteractive)),(y||E||w)&&a.push(p),E&&c.push(p)}}),await Vn(l),{interactiveElements:c,reportedElements:a,imageElements:l}}function Ge(o){let i=[];return Object.keys(o.map).forEach(c=>{let a=o.map[c];a.attributes||(a.attributes={}),a.isTopElement&&(a.isInViewport||typeof a.isInViewport>"u")&&(a.id=c,a.isInteractive&&((a.wxEvents||[]).length>0||["button","a"].includes(a.tagName||""))&&i.push(a))}),i}function Ut(o={isInteractive:!0,doHighlightElements:!1}){let i=yt({doHighlightElements:o?.doHighlightElements||!1,focusHighlightIndex:-1,viewportExpansion:0,debugMode:!1,recordRects:!0});return Ge(i).map(l=>{let{rect:v,tagName:p}=l;return{rect:v,tagName:p}})}function fe(o,i=0){let c="  ".repeat(i),a=$n(o),l=o.children&&o.children.length>0,v=o.textContent||o.innerText||o.nodeValue||"",p=v!=="",y=o.tagName?.toLowerCase()||o.nodeName?.toLowerCase();if(!y)return"";y.startsWith("#")&&(y=y.slice(1));let E=`${c}<${y}${a}`;if(!l&&!p)return`${E} />
`;if(E+=">",p&&!l){let w=v;return v.includes(`
`)&&(w=v.split(`
`).join("  ")),`${E}${$t(w)}</${y}>
`}return E+=`
`,o.children&&(o.children.forEach(w=>{E+=fe(w,i+1)}),p&&(E+=`${c}  ${$t(v)}
`)),E+=`${c}</${y}>
`,E}function $n(o){if(!o)return"";let i=[],c=o.id||o.nodeId;if(c&&i.push(`id="${c}"`),typeof o.attributes=="object"&&o.attributes)if(Array.isArray(o.attributes))o.attributes&&o.attributes.length>0&&i.push(`attr="${ze(o.attributes)}"`);else for(let l in o.attributes)o.attributes[l]!==void 0&&i.push(`${l}="${$t(o.attributes[l])}"`);let a=o.events||o.wxEvents;return a&&a.length>0&&i.push(`event="${ze(a)}"`),o.classList&&o.classList.length>0&&i.push(`class="${o.classList.join(" ")}"`),o.zIndex&&i.push(`z="${o.zIndex}"`),i.length>0?` ${i.join(" ")}`:""}function ze(o){return`[${o.map(i=>`'${$t(i)}'`).join(", ")}]`}function $t(o){return o?o.replace(/[&<>'"]/g,i=>{switch(i){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case'"':return"&quot;";case"'":return"&apos;";default:return i}}):""}function mt(o,i=pe()){let c=document.evaluate(o,i||document,null,XPathResult.FIRST_ORDERED_NODE_TYPE,null);return console.log("parent:",i),console.log("xPathResult:",c),c.singleNodeValue}function qe(o){let c=mt(o)?.getBoundingClientRect();if(!c){console.error("\u672A\u627E\u5230\u5143\u7D20");return}let{left:a,top:l,width:v,height:p}=c,y=a+v/2,E=l+p/2;return{centerX:y,centerY:E,width:v,height:p}}function pe(){let o=window.innerWidth/2,i=window.innerHeight/2,c=document.elementFromPoint(o,i);return c?c.contentDocument:document}function de(){let o=window.innerWidth/2,i=window.innerHeight/2,c=document.elementFromPoint(o,i);for(;c&&!["body","iframe"].includes(c?.tagName.toLowerCase());)c=c.parentElement;let l=c?.getBoundingClientRect();return{offsetX:l?.x||0,offsetY:l?.y||0,rect:xt(l)}}function Je(){let{offsetX:o,offsetY:i}=de(),c=Ut({});return c.forEach(a=>{a.rect&&(a.rect.x+=o,a.rect.y+=i)}),c}function Ze(o={doHighlightElements:!1}){let i=yt({doHighlightElements:o.doHighlightElements,focusHighlightIndex:-1,viewportExpansion:0,skipIframe:!0,recordRects:!0}),c=[];return Object.keys(i.map).forEach(a=>{let l=i.map[a];l.isTopElement&&(l.isInViewport||typeof l.isInViewport>"u")&&l.isVisible&&l.isInteractive&&c.push(l)}),c.map(a=>({tagName:a.tagName,rect:a.rect}))}function Yn(o){let i=o.split("/");if(i[1]==="native"&&i[2]==="tab-bar"){let a=i[3].split("tabbar-item")[1].replace("[","").replace("]","");return document.querySelector(`#container > div.tab_bar > div > div.tabbar_item.tabbar_item_${a}`)}}function Qe(o){let i=Yn(o.xpath);if(!i){Nt.info("native element not found");return}o.event==="click"&&i.click()}function Ke(){let o=[],i=mt('/html/body//*[text()="\u540C\u610F"]');i&&o.push(i);let c=mt('/html/body//*[text()="\u62D2\u7EDD"]');c&&o.push(c),o.forEach(a=>{a.setAttribute("event","tap")}),console.log("tapElements",o)}window.__WX_WM_SANDBOX__={DEBUG_LEVEL:0,getElementByXpath:mt,getCurrentDocument:pe,getElementPosByXpath:qe,operateNativeElement:Qe,getDomXml:jn,getAllElementsRects:Ut,getIframeOffset:de,getAllElementsRectsWithOffset:Je,getAllNativeElementsRects:Ze};async function jn(){Ke();let o=yt({doHighlightElements:window.__WX_WM_SANDBOX__.DEBUG_LEVEL>=2,focusHighlightIndex:-1,viewportExpansion:0,debugMode:window.__WX_WM_SANDBOX__.DEBUG_LEVEL>=2}),{reportedElements:i}=await je(o),c=i.map(w=>{let{attributes:R={},...H}=w,$=Object.keys(R).reduce((ht,f)=>(Be(f,R)&&(ht[f]=R[f]),ht),{});return{...H,attributes:$}}),a=ke(c),l=fe(a);Nt.debug("xml",l);let v=[];c.forEach(w=>{if(w.xpath?.startsWith("html/body")){let R=mt(w.xpath,document)?.parentElement;R?.classList.contains("tabbar_item")&&(w.classList=R.classList,v.push(w))}}),Nt.debug("nativeElements",v);let p="<tab-bar>";v.forEach(w=>{let R=w.classList[1]?.replace("tabbar_item_",""),H=w.textContent;p+=`  <tabbar-item index="${R}" event="['tap']">${H}</tabbar-item>
`}),p+="</tab-bar>";let y=v.length>0?`<native>${p}</native>`:"";return`<page>
  ${l}${y}
</page>`}})();
/*! Bundled license information:

hammerjs/hammer.js:
  (*! Hammer.JS - v2.0.7 - 2016-04-22
   * http://hammerjs.github.io/
   *
   * Copyright (c) 2016 Jorik Tangelder;
   * Licensed under the MIT license *)
*/