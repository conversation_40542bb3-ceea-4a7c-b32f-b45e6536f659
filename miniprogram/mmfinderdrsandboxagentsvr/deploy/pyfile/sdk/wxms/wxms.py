"""
沙箱操作的核心模块
"""

import base64
import json
import logging
import os
import random
import time
import uuid
from typing import Any

import requests

from .model import (
    APPLET_FIXED_HEIGHT,
    APPLET_FIXED_WIDTH,
    INTERRUPT_ERROR_CODE_DICT,
    InterruptError,
)
from .util import (
    decode_csv_to_xml,
    get_base_url,
    get_sandbox_js_api_runtime_evaluate_params,
    image_save,
    translate_coordinate_from_fixed_to_real,
)

logging.basicConfig(
    level=logging.INFO,
    filename="wxms.log",
    filemode="a",
    format="%(asctime)s - %(levelname)s - %(message)s",
)


def base_cdp_proxy_headless_process_result(result) -> dict:
    """
    处理无头小程序的指令操作结果
    """
    if not result:
        return {"code": -1, "message": "cdp_response empty"}
    _ret_code = result.get("ret_code", 0)
    if _ret_code != 0:
        return {
            "code": _ret_code,
            "message": f"get_cdp_response ret_code err, code: {_ret_code}",
        }
    _cdp_error = result.get("cdp_error", 0)
    if _cdp_error != 0:
        return {
            "code": _cdp_error,
            "message": f"get_cdp_response cdp_error, code: {_cdp_error}",
        }
    # TODO: list
    result = result.get("cdp_response", [])
    if len(result) == 0 or not result[0]:
        return {"code": -1, "message": "cdp_response empty"}
    base_result = json.loads(result[0])
    if "data" in base_result and base_result["data"].get("error_code", 0) != 0:
        _code = base_result["data"].get("error_code", 0)
        _msg = INTERRUPT_ERROR_CODE_DICT.get(_code, "user interrupted")
        _msg = base_result["data"].get("err_msg", _msg)
        err_msg = (
            "cdp_response interruped"
            + f"error_code: {_code}, msg: {_msg}, data: {base_result['data']}"
        )
        logging.error(err_msg)
        raise InterruptError(_msg, base_result["data"])
    if "data" in base_result and "result" not in base_result:
        base_result["result"] = base_result["data"]
    return {"code": 0, "data": base_result}


def base_cdp_proxy_headless(
    _to_uin, _to_username, _request_id, data, _from_username=""
) -> dict:
    """
    下发无头小程序的指令并获取操作结果
    """
    from .miniprogram import (  # type: ignore # pylint: disable=E0401, C0415
        get_cdp_response,
        send_cdp_command,
    )

    cmd_id = str(uuid.uuid4())
    timeout = 60000
    # TODO: hardcode uin
    from_uin = 1111
    from_username = _from_username
    to_username = _to_username
    log_data = []
    try:
        if isinstance(data, dict):
            data = [json.dumps(data, ensure_ascii=False)]
        elif isinstance(data, list):
            data = [json.dumps(item, ensure_ascii=False) for item in data]
        msg = (
            "send cdp command debug:"
            + f"cmd_id={cmd_id}, request_id={_request_id}, "
            + f"uin={_to_uin}, username={_to_username}, from_username={_from_username}, "
            + f"data={data}"
        )
        logging.info(msg)
        # TODO: refactor
        ret = send_cdp_command(
            from_uin,
            _to_uin,
            from_username,
            to_username,
            _request_id,
            cmd_id,
            data,
            log_data,
        )
        msg = msg + f", ret={ret}, log_data={log_data}"
        logging.info(msg)
        if ret != 0:
            return {"code": ret, "message": f"send_cdp_command err, ret {ret}"}
        result = get_cdp_response(_to_uin, _request_id, cmd_id, timeout)
        msg = msg + f", result={result}"
        logging.info(msg)
        return base_cdp_proxy_headless_process_result(result)
    except InterruptError as e:
        raise e
    except Exception as e:  # pylint: disable=W0718
        msg = (
            f"send cdp command failed: {e}, cmd_id={cmd_id}, request_id={_request_id}, "
            + f"uin={_to_uin}, username={_to_username}, from_username={_from_username}, "
            + f"data={data}, log_data={log_data}"
        )
        logging.error(msg)
        return {"code": -1, "message": f"cdp_proxy_headless err, {e}"}


# 无头小程序中特有的 XWeb 事件
# XWeb.GetFilteredDomXml
# XWeb.GetAllElementsRects
# XWeb.ShareAppMessage
class WXMSPage:
    """
    小程序的操作
    """

    def __init__(self, user_id: str, target_id: str):
        self.user_id = user_id
        self.target_id = target_id
        self.username = os.getenv("WXMS_USERNAME") or ""
        self.from_username = os.getenv("WXMS_FROM_USERNAME") or ""
        self.request_id = os.getenv("WXMS_REQUEST_ID") or ""
        self.uin = int(os.getenv("WXMS_UIN") or "0")
        self.headless_mode = int(os.getenv("WXMS_HEADLESS_MODE") or "0")
        self.real_size = (-1, -1)
        self.resized_size = (APPLET_FIXED_WIDTH, APPLET_FIXED_HEIGHT)
        self.touch_event_id = 0

    def __cdp_proxy(self, data_list: list[dict]) -> dict:
        for data in data_list:
            if "target_id" not in data:
                data["target_id"] = self.target_id
        if self.headless_mode:
            return self.__cdp_proxy_headless(data_list)
        else:
            res = {}
            for data in data_list:
                # TODO 后续 http 的也要支持多个 cdp 指令的调用下发
                res = self.__cdp_proxy_http(data)
            return res

    def __cdp_proxy_headless(self, data_list: list[dict]) -> dict:
        return base_cdp_proxy_headless(
            self.uin, self.username, self.request_id, data_list, self.from_username
        )

    def __cdp_proxy_http(self, data: dict) -> dict:
        url = f"{get_base_url()}/v1/sandbox/cdp_proxy/{self.user_id}"
        return requests.post(url, json=data, timeout=10).json()

    def __get_touch_event_id(self) -> int:
        self.touch_event_id += 1
        return self.touch_event_id

    def __input_dispatch_key_event(self, params_list: list[dict]):
        data = [{"method": "Input.dispatchKeyEvent", "params": p} for p in params_list]
        return self.__cdp_proxy(data)

    def __input_dispatch_touch_event(self, params_list: list[dict]):
        data_list = [
            {
                "method": "Input.dispatchTouchEvent",
                "params": p,
            }
            for p in params_list
        ]
        return self.__cdp_proxy(data_list)

    def __mock_cdp_resp(self, res_code: int, value: Any) -> dict:
        return {"code": res_code, "data": {"result": {"result": {"value": value}}}}

    def dom_enable(self):
        """
        https://chromedevtools.github.io/devtools-protocol/tot/DOM/#method-enable
        启用 DOM 代理，无头小程序是移动客户端所以不需要做处理
        """
        if self.headless_mode:
            return
        data = {
            "method": "DOM.enable",
            "params": {},
        }
        self.__cdp_proxy([data])

    def get_all_elements_rects_with_offset(self) -> tuple[dict, dict]:
        """
        获取当前页面所有操作元素的框矩形信息，矩形信息会带上 webview 的偏移量，从而保证坐标原点在最左上角

        :return: 出参格式不管是不是无头小程序都要保持一致，第一个参数是非原生的列表，第二个是原生的列表
        """
        if self.headless_mode:
            resp = self.__cdp_proxy(
                [
                    {
                        "method": "XWeb.GetAllElementsRects",
                        "params": {
                            "task_id": str(uuid.uuid4()),
                        },
                    }
                ]
            )
            elements_rects_list = []
            native_elements_rects_list = []
            res_code = resp.get("code", 0)
            if res_code == 0:
                # 非原生元素，需要带上偏移量
                temp_str = (
                    resp.get("data", {}).get("data", {}).get("elements_rects", "")
                )
                elements_rects_list = json.loads(temp_str) if temp_str else []
                elements_rects_list = [
                    {
                        "tagName": x["tagName"],
                        "rect": {k: int(v) for k, v in x["rect"].items()},
                    }
                    for x in elements_rects_list
                ]
                webview_offset_y = (
                    resp.get("data", {}).get("data", {}).get("webview_offsetY", 0)
                )
                for rect in elements_rects_list:
                    rect["rect"]["y"] += webview_offset_y
                # 原生元素，自动带上偏移量，无需额外添加
                temp_str = (
                    resp.get("data", {})
                    .get("data", {})
                    .get("native_elements_rects", "")
                )
                native_elements_rects_list = json.loads(temp_str) if temp_str else []
                native_elements_rects_list = [
                    {
                        "tagName": x["tagName"],
                        "rect": {k: int(v) for k, v in x["rect"].items()},
                    }
                    for x in native_elements_rects_list
                ]
            elements_rects = self.__mock_cdp_resp(res_code, elements_rects_list)
            native_elements_rects = self.__mock_cdp_resp(
                res_code, native_elements_rects_list
            )
            return elements_rects, native_elements_rects
        else:
            elements_rects = self.__cdp_proxy(
                [
                    get_sandbox_js_api_runtime_evaluate_params(
                        "getAllElementsRectsWithOffset()"
                    )
                ]
            )
            native_elements_rects = self.__cdp_proxy(
                [
                    get_sandbox_js_api_runtime_evaluate_params(
                        "getAllNativeElementsRects()"
                    )
                ]
            )
            return elements_rects, native_elements_rects

    def get_dom_xml(self) -> dict:
        """
        获取当前页面的 dom xml

        :return: 出参格式不管是不是无头小程序都要保持一致
        """
        if self.headless_mode:
            resp = self.__cdp_proxy(
                [
                    {
                        "method": "XWeb.GetFilteredDomXml",
                        "params": {
                            "task_id": str(uuid.uuid4()),
                            "role": "server",
                        },
                    }
                ]
            )
            dom_xml_value = ""
            res_code = resp.get("code", 0)
            if res_code == 0:
                dom_xml = resp.get("data", {}).get("data", {}).get("dom_xml", "")
                dom_xml_value = (
                    "<html></html>" if dom_xml == "" else decode_csv_to_xml(dom_xml)
                )
            return self.__mock_cdp_resp(res_code, dom_xml_value)
        else:
            return self.__cdp_proxy(
                [get_sandbox_js_api_runtime_evaluate_params("getDomXml()")]
            )

    def simple_click_left(self, x: int, y: int, fixed: bool = True):
        """
        左键点击操作，使用 touch 事件而不是鼠标事件来模拟，因为触摸屏没有鼠标

        :param x: 点击的横坐标
        :param y: 点击的纵坐标
        :param fixed: x 和 y 的坐标是否是固定尺寸的坐标，
            截图的时候会将设备的屏幕尺寸固定缩放至 APPLET_FIXED_WIDTH * APPLET_FIXED_HEIGHT 的框中
            从而导致视觉模型推理结果的坐标不是设备上的真实坐标
            所以如果是固定尺寸坐标系上的坐标，则需要转换成设备上的真实坐标
        """
        touch_event_id = self.__get_touch_event_id()
        touch_points = [{"x": x, "y": y, "id": touch_event_id}]
        logging.info(  # pylint: disable=W1203
            f"simple_click_left should click: {touch_points}"
        )
        if fixed:
            if self.real_size[0] <= 0:
                # update real size
                self.simple_screenshot()
            real_xy = translate_coordinate_from_fixed_to_real(
                (x, y),
                self.real_size,
                self.resized_size,
            )
            touch_points = [
                {
                    "x": real_xy[0],
                    "y": real_xy[1],
                    "id": touch_event_id,
                }
            ]
            logging.info(  # pylint: disable=W1203
                f"simple_click_left real click: {touch_points}"
            )
        start_params = {
            "task_id": str(uuid.uuid4()),
            "type": "touchStart",  # 事件类型（touchStart/touchMove/touchEnd）
            "touchPoints": touch_points,
        }
        end_params = {
            "task_id": str(uuid.uuid4()),
            "type": "touchEnd",
            "touchPoints": touch_points,
        }
        self.__input_dispatch_touch_event([start_params, end_params])

    def simple_click_left_by_xpath(self, xpath: str):
        """
        左键点击操作，入参是 xpath 而不是坐标
        """
        if self.headless_mode:
            touch_event_id = self.__get_touch_event_id()
            start_params = {
                "task_id": str(uuid.uuid4()),
                "type": "touchStart",  # 事件类型（touchStart/touchMove/touchEnd）
                "id": touch_event_id,
                "touchXpath": xpath,
            }
            end_params = {
                "task_id": str(uuid.uuid4()),
                "type": "touchEnd",
                "id": touch_event_id,
                "touchXpath": xpath,
            }
            self.__input_dispatch_touch_event([start_params, end_params])
        elif "native" in xpath:
            self.__cdp_proxy(
                [
                    get_sandbox_js_api_runtime_evaluate_params(
                        f"operateNativeElement({{ event: 'click', xpath: '{xpath}' }})"
                    )
                ]
            )
        else:
            logging.error(  # pylint: disable=W1203
                f"沙箱不支持非原生元素的点击: {xpath}"
            )

    def simple_paste_text(self, text: str):
        """
        https://chromedevtools.github.io/devtools-protocol/tot/Input/#method-insertText

        :param text: 需要粘贴输入的文本
        """
        return self.__cdp_proxy(
            [
                {
                    "method": "Input.insertText",
                    "params": {
                        "text": text,
                    },
                }
            ]
        )

    def simple_press_enter(self):
        """
        按下并释放回车键
        """
        # 定义按下回车键的参数
        down_param = {
            "type": "keyDown",
            "key": "Enter",
            "keyCode": 0x0D,
            "windowsVirtualKeyCode": 0x0D,
            "code": "Enter",
            "unmodifiedText": "\n",
            "text": "\n",
        }
        # 定义释放回车键的参数
        up_param = {
            "type": "keyUp",
            "key": "Enter",
            "keyCode": 0x0D,
            "windowsVirtualKeyCode": 0x0D,
            "code": "Enter",
            "unmodifiedText": "\n",
            "text": "\n",
        }
        self.__input_dispatch_key_event([down_param, up_param])

    def simple_screenshot(
        self, save_path: str | None = None, compression_quality: float = 0.99
    ):
        """
        获取当前页面的截图

        :param save_path: 图片保存路径，为 None 时则不保存
        :param compression_quality: 截图的图片质量
        """
        # 获取截图二进制数据
        params = {}
        if self.headless_mode:
            params = {
                "task_id": str(uuid.uuid4()),
                "compression_quality": compression_quality,
            }
        screenshot_data = self.__cdp_proxy(
            [
                {
                    "method": "Page.captureScreenshot",
                    "params": params,
                }
            ]
        )
        image_bytes = screenshot_data.get("data", {}).get("result", {}).get("data", "")
        if image_bytes == "":
            raise ValueError(f"Failed to capture screenshot: {screenshot_data}")
        # 解码为二进制数据
        image_bytes = base64.b64decode(image_bytes)
        logging.info(  # pylint: disable=W1203
            f"screenshot captured image_bytes size: {len(image_bytes)}"
        )
        self.real_size, self.resized_size = image_save(save_path, image_bytes)

    def simple_scroll(self, delta_x: int = 0, delta_y: int = 100):
        """
        滚动页面

        :param delta_x: 水平滚动距离
        :param delta_y: 垂直滚动距离
        """
        x = 200
        y = 200
        touch_event_id = self.__get_touch_event_id()
        start_params = {
            "task_id": str(uuid.uuid4()),
            "type": "touchStart",  # 事件类型（touchStart/touchMove/touchEnd）
            "touchPoints": [{"x": x, "y": y, "id": touch_event_id}],
        }
        move_params = {
            "task_id": str(uuid.uuid4()),
            "type": "touchMove",  # 事件类型（touchStart/touchMove/touchEnd）
            "touchPoints": [{"x": x + delta_x, "y": y + delta_y, "id": touch_event_id}],
        }
        end_params = {
            "task_id": str(uuid.uuid4()),
            "type": "touchEnd",
            "touchPoints": [{"x": x + delta_x, "y": y + delta_y, "id": touch_event_id}],
        }
        self.__input_dispatch_touch_event([start_params, move_params, end_params])


class WXMS:
    """
    微信的操作
    """

    def __init__(self):
        self.username = os.getenv("WXMS_USERNAME") or ""
        self.from_username = os.getenv("WXMS_FROM_USERNAME") or ""
        self.request_id = os.getenv("WXMS_REQUEST_ID") or ""
        self.uin = int(os.getenv("WXMS_UIN") or "0")
        self.headless_mode = int(os.getenv("WXMS_HEADLESS_MODE") or "0")
        self.user_id = self.uin if self.headless_mode else -1
        self.stop = False

    def __cdp_proxy(self, data_list: list[dict]) -> dict:
        if self.headless_mode:
            return self.__cdp_proxy_headless(data_list)
        else:
            res = {}
            for data in data_list:
                # TODO 后续 http 的也要支持多个 cdp 指令的调用下发
                res = self.__cdp_proxy_http(data)
            return res

    def __cdp_proxy_headless(self, data_list: list[dict]) -> dict:
        return base_cdp_proxy_headless(
            self.uin, self.username, self.request_id, data_list, self.from_username
        )

    def __cdp_proxy_http(self, data: dict) -> dict:
        url = f"{get_base_url()}/v1/sandbox/cdp_proxy/{self.user_id}"
        response = requests.post(url, json=data, timeout=10)
        logging.info(response.text)
        logging.info(response.status_code)
        return response.json()

    def __xweb_close_applet(self, app_id: str, with_card: bool) -> dict:
        data = [
            {
                "method": "XWeb.CloseApplet",
                "params": {
                    "role": "server",
                    "task_id": str(random.randint(0, 10000000)),
                    "app_id": app_id,
                },
            }
        ]
        if self.headless_mode and with_card:
            data.insert(
                0,
                {
                    "method": "XWeb.ShareAppMessage",
                    "params": {
                        "role": "server",
                        "task_id": str(random.randint(0, 10000000)),
                    },
                },
            )
        return self.__cdp_proxy(data)

    def __xweb_launch_applet(self, app_id: str) -> dict:
        """
        打开小程序

        :param app_id: 小程序的 id
        """
        data = {
            "method": "XWeb.LaunchApplet",
            "params": {
                "role": "server",
                "task_id": str(random.randint(0, 10000000)),
                "app_id": app_id,
                "entry_url": "",
            },
        }
        if self.headless_mode == 0:
            data["params"]["width"] = APPLET_FIXED_WIDTH
            data["params"]["height"] = APPLET_FIXED_HEIGHT
        elif self.headless_mode == 1:
            data["params"]["headless"] = 1
        return self.__cdp_proxy([data])

    def __xweb_targets(self) -> dict:
        # 无头小程序中不支持该操作
        return self.__cdp_proxy(
            [
                {
                    "method": "XWeb.targets",
                    "params": {
                        "role": "server",
                    },
                }
            ]
        )

    def close(self, app_id: str, with_card: bool = False):
        """
        释放微信实例。当 WXMS 不再使用时，必须调用该方法，释放远端沙箱资源

        :param app_id: 小程序的 id
        :param with_card: 仅在无头小程序中能够生效
        """
        self.stop = True
        if self.headless_mode:
            # TODO 后续不管是不是无头都要关闭
            try:
                self.__xweb_close_applet(app_id, with_card)
            except InterruptError:
                pass

    def simple_get_app(self, app_id: str, close_old: bool = True) -> WXMSPage:
        """
        启动指定的小程序，并返回一个 WXMSPage 对象用于与页面交互

        :param app_id: 小程序的 id
        :param close_old: 是否关闭已打开的小程序，只有在非无头小程序的场景下才生效
        :return: WXMSPage 对象
        """
        if self.headless_mode:
            self.__xweb_launch_applet(app_id)
            return WXMSPage(str(self.user_id), app_id)

        # 如果 close_old 为 True，关闭已打开的小程序
        if close_old:
            targets = self.__xweb_targets()
            for target in targets["data"]["data"]:
                if "url" in target and app_id in target["url"]:
                    self.__xweb_close_applet(app_id, False)
                    logging.info(  # pylint: disable=W1203
                        f"Closed existing applet with app_id={app_id}"
                    )
                    time.sleep(1)
                    break
        # 启动指定的小程序
        self.__xweb_launch_applet(app_id)
        logging.info(f"Launched applet with app_id={app_id}")  # pylint: disable=W1203
        time.sleep(5)
        # 获取目标页面信息
        targets = self.__xweb_targets()
        for target in targets["data"]["data"]:
            if "url" in target and app_id in target["url"]:  # 根据 URL 匹配
                # 返回 WXMSPage 对象
                return WXMSPage(str(self.user_id), target["id"])
        raise ValueError(f"no target found for app_id={app_id}")

    def xweb_get_applet_page_info(self, app_id: str) -> dict:
        """
        获取已打开的小程序的当前页面信息

        :param app_id: 小程序的 id
        """
        return self.__cdp_proxy(
            [
                {
                    "method": "XWeb.GetAppletPageInfo",
                    "params": {
                        "role": "server",
                        "task_id": str(random.randint(0, 10000000)),
                        "app_id": app_id,
                    },
                }
            ]
        )


def __test_wxms():
    wxms_instance = WXMS()
    data = {
        "method": "XWeb.RequestLogout",
        "params": {
            "role": "server",
        },
    }
    wxms_instance.__cdp_proxy([data])  # pylint: disable=W0212

    # a = "1_BgAAlAh0qcM3wZAlVDndFYFKxzQPGwSKbzUxRmRK"
    # data = {
    #     "method": "XWeb.RequestLogin",
    #     "params": {
    #         "role": "server",
    #         "auth_code": a,
    #     }
    # }

    # wxms_instance.cdp_proxy(data)
    # wxms_page = wxms_instance.simple_get_app("wx8a5d6f9fad07544e", close_old=True)
    # xml_content = wxms_page.get_dom_xml()


if __name__ == "__main__":
    __test_wxms()
