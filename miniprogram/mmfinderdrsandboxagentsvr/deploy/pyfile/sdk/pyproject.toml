[tool.poetry]
name = "wxms"
version = "2.0.0"
description = "A Python package for interacting with WeChat Mini Programs via CDP (Chrome DevTools Protocol)."
authors = ["mikexinchen <<EMAIL>>", "yuy<PERSON><PERSON><PERSON> <yuyi<PERSON><EMAIL>>"]
license = "MIT"

[tool.poetry.dependencies]
httpx = "^0.28.1"
openai = "^1.83.0"
pillow = "^11.2.1"
py-mini-racer = "^0.6.0"
pydantic = "^2.11.5"
pydantic-settings = "^2.9.1"
python = "^3.12"

[[tool.poetry.source]]
name = "tencent_pypi"
url = "https://mirrors.cloud.tencent.com/pypi/simple/"
priority = "primary"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
