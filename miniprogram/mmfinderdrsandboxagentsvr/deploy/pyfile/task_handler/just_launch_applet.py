"""仅仅是打开小程序并截图"""

import os
import random
import time

import wxms
from model import PythonInput, PythonResult, PythonResultStatus


def __get_and_create_screenshot_dir() -> str:
    root_folder = f"{os.path.dirname(__file__)}/outputs"
    folder_name = os.path.basename(__file__).replace(".py", "")
    timestamp = time.strftime("%Y%m%d_%H%M%S_%f")[:-3]
    random_id = "".join(random.sample("0123456789abcdef", 8))
    screenshot_dir = (
        f"{root_folder}/{folder_name}/cache_screenshot_{timestamp}_{random_id}"
    )
    os.makedirs(screenshot_dir)
    return screenshot_dir


def handle_task(python_input: PythonInput) -> PythonResult:
    """仅仅是打开小程序并截图"""
    wxms_instance = wxms.WXMS()
    wxms_page = wxms_instance.simple_get_app(python_input.app_id, close_old=True)
    time.sleep(1)
    screenshot_dir = __get_and_create_screenshot_dir()
    screenshot_name = f"{screenshot_dir}/init.jpg"
    wxms_page.simple_screenshot(screenshot_name)
    wxms_instance.close(python_input.app_id)
    return PythonResult(
        answers=[],
        answers_raw_data=[],
        interrupt={},
        label_indexs=[],
        screens=[],
        screenshots=[screenshot_name],
        status=PythonResultStatus.SUCCESS,
        target_id=wxms_page.target_id,
        xmls=[],
        xpaths=[],
    )
