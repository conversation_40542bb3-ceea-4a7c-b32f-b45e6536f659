"""自动注册各个推理逻辑的 task handler"""

import importlib.util
import os
from typing import Optional

from model import TaskhandlerProtocol

REGISTRY = {}


def get_task_handler(task_type: str) -> Optional[TaskhandlerProtocol]:
    """获取 task_handler 函数"""
    return REGISTRY.get(task_type)


for i in os.walk(os.path.dirname(__file__)):
    folder, x, files = i
    for file in files:
        if not file.endswith(".py") or file in ["__init__.py"]:
            continue
        module_name = file.split(".")[0]
        file_path = os.path.join(folder, file)
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        if spec is None or spec.loader is None:
            continue
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        REGISTRY[file[:-3]] = module.handle_task
