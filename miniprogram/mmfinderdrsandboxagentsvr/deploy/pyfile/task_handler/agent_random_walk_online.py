import base64
import json
import os
import random
import signal
import sys
import time
import uuid

import openai
import wxms
from model import PythonIn<PERSON>, PythonResult, PythonResultStatus

headless_mode = int(os.getenv("WXMS_HEADLESS_MODE") or "0")


def sleep(seconds, force=False):
    if headless_mode and not force:
        time.sleep(seconds * 0.0001)
        return
    time.sleep(seconds)


def get_and_create_screenshot_dir() -> str:
    root_folder = f"{os.path.dirname(__file__)}/outputs"
    folder_name = os.path.basename(__file__).replace(".py", "")
    timestamp = time.strftime("%Y%m%d_%H%M%S_%f")[:-3]
    random_id = "".join(random.sample("0123456789abcdef", 8))
    screenshot_dir = (
        f"{root_folder}/{folder_name}/cache_screenshot_{timestamp}_{random_id}"
    )
    os.makedirs(screenshot_dir)
    return screenshot_dir


def image_to_base64(image_path):
    try:
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read())
            return encoded_string.decode("utf-8")
    except FileNotFoundError:
        print(f"错误：未找到指定的图片文件: {image_path}", file=sys.stderr)
    except Exception as e:
        print(f"错误：发生了未知错误: {str(e)}", file=sys.stderr)
        print(f"错误图片路径: {image_path}", file=sys.stderr)


def Qwen25VL72BInstruct(img, vl_prompt):
    try:
        client = openai.OpenAI(
            api_key="EMPTY",
            base_url="http://drhttpsvr.polaris:8000/v1/llm-luban-xcx-Qwen2.5-VL-72B-Instruct",
        )
        reasoning_content = ""  # 定义完整思考过程
        answer_content = ""  # 定义完整回复
        is_answering = False  # 判断是否结束思考过程并开始回复
        input_imgs = [img]

        input_imgs_content = [
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_to_base64(img)}"},
            }
            for img in input_imgs
        ]
        messages = [
            {
                "role": "user",
                "content": input_imgs_content + [{"type": "text", "text": vl_prompt}],
            }
        ]
        # print('vl: ', len(messages[0]['content']))
        completion = client.chat.completions.create(
            model="llm-luban-xcx-Qwen2.5-VL-72B-Instruct",
            messages=messages,
            temperature=0.9,
            seed=2025,
            stream=True,
        )
        for chunk in completion:
            # 处理usage信息
            if not getattr(chunk, "choices", None):
                print("\n" + "=" * 20 + "Token 使用情况" + "=" * 20 + "\n")
                print(chunk.usage)
                continue

            delta = chunk.choices[0].delta

            # 处理空内容情况
            if not getattr(delta, "reasoning_content", None) and not getattr(
                delta, "content", None
            ):
                continue

            # 处理开始回答的情况
            if not getattr(delta, "reasoning_content", None) and not is_answering:
                # print("\n" + "=" * 20 + "完整回复" + "=" * 20 + "\n")
                is_answering = True

            # 处理思考过程
            if getattr(delta, "reasoning_content", None):
                # print(delta.reasoning_content, end='', flush=True)
                reasoning_content += delta.reasoning_content
            # 处理回复内容
            elif getattr(delta, "content", None):
                # print(delta.content, end='', flush=True)
                answer_content += delta.content
        return answer_content

    except Exception as e:
        print(f"捕获到异常: {str(e)}", file=sys.stderr)
        return ""


class Agent:
    def __init__(self, wxms_instance, app_id, log_dir="logs"):
        self.wxms_instance = wxms_instance  # 小程序操作接口
        self.running = False  # 控制 agent 是否运行

        self.step_count = 0
        self.episode_count = 0
        self.app_id = app_id
        self.task_id = str(uuid.uuid4())[:8]  # 短 UUID

        self.wxms_page = self.wxms_instance.simple_get_app(self.app_id, close_old=True)
        sleep(1)

        # 初始化日志和截图目录
        self.log_dir = log_dir
        self.image_dir = get_and_create_screenshot_dir()
        os.makedirs(self.log_dir, exist_ok=True)

        # 日志文件
        int(time.time())
        self.log_file = os.path.join(log_dir, f"agent_log.jsonl")
        self.log_data = []
        self.all_screenshots = []

    def generate_image_path(self, phase="before"):
        """生成带时间戳和 app_id 的唯一截图路径"""
        timestamp_ms = int(time.time() * 1000)  # 毫秒级时间戳
        filename = f"{self.app_id}_{self.task_id}_ep{self.episode_count:05d}_{timestamp_ms}_{phase}_{self.step_count:04d}.jpg"
        return os.path.join(self.image_dir, filename)

    def retry_screenshot(self, image_path, max_retries=3, delay=2):
        for attempt in range(1, max_retries + 1):
            try:
                # 调用原始截图方法
                self.wxms_page.simple_screenshot(image_path)
                return True
            except Exception as e:
                print(f"截图失败（第 {attempt} 次尝试）: {str(e)}")
                if attempt < max_retries:
                    sleep(delay, force=True)
                else:
                    print(f"截图失败，已达到最大重试次数: {max_retries}")
        return False

    def take_screenshot_before(self):
        before_img = self.generate_image_path("before")
        success = self.retry_screenshot(before_img)
        return before_img, success

    def take_screenshot_after(self):
        sleep(3, force=True)
        after_img = self.generate_image_path("after")
        success = self.retry_screenshot(after_img)
        sleep(1)
        return after_img, success

    def log_step(self, before_img, action, after_img, rect):
        data = {
            "before_image": before_img,
            "action": action,
            "after_image": after_img,
            "task_id": self.task_id,
            "rect": rect,
        }
        with open(self.log_file, "a", encoding="utf-8") as f:
            f.write(json.dumps(data, ensure_ascii=False) + "\n")

        self.log_data.append(json.dumps(data, ensure_ascii=False))
        self.all_screenshots.append(before_img)
        self.all_screenshots.append(after_img)

    def get_log_data(self):
        return self.log_data

    # 新增方法：获取图像数据
    def get_screenshots(self):
        return self.all_screenshots

    def parse_rect_data(self, raw_data):
        result = []
        if not raw_data or raw_data.get("code") != 0:
            print("框数据获取失败")
            return []

        elements = (
            raw_data.get("data", {})
            .get("result", {})
            .get("result", {})
            .get("value", [])
        )
        for idx, item in enumerate(elements):
            rect_info = item.get("rect")
            tag_name = item.get("tagName", "unknown")
            if rect_info and all(k in rect_info for k in ["x", "y", "width", "height"]):
                x = rect_info["x"]
                y = rect_info["y"]
                width = rect_info["width"]
                height = rect_info["height"]
                x1 = x + width
                y1 = y + height
                # 过滤太大的操作框
                if width * height > 300 * 300:
                    continue
                # 判断 action 类型
                if tag_name == "wx-input":
                    action = "input"
                else:
                    action = "click"

                # 转换为 [x, y, width, height] 格式
                result.append(
                    {
                        "id": f"{tag_name}-{idx}",
                        "rect": [int(x), int(y), int(x1), int(y1)],
                        "action": action,
                    }
                )

        print(f"成功解析 {len(result)} 个可操作元素")
        return result

    def _handle_click(self, element):
        rect = element["rect"]
        x_center = int((rect[0] + rect[2]) // 2)
        y_center = int((rect[1] + rect[3]) // 2)
        self.wxms_page.simple_click_left(x=x_center, y=y_center, fixed=False)
        sleep(1)
        return "click " + str(x_center) + " " + str(y_center)

    def _handle_input(self, element, before_img):
        rect = element["rect"]
        x_center = int((rect[0] + rect[2]) // 2)
        y_center = int((rect[1] + rect[3]) // 2)

        prompt = """你是一个多模态搜索词预测专家。请严格按以下步骤分析：
        1. **视觉定位**：
        - 已收到用户提供的界面截图和点击坐标 (x={0}, y={1})。
        - 定位该坐标所在的UI元素（优先判断是否为输入框，其次检查附近文本/图标）。

        2. **场景推理**（若为输入框）：
        - 识别整个界面的功能类型（如电商/音乐/地图/社交）。
        - 提取输入框附近的视觉线索：
            * 占位符文字（如"搜索商品"）
            * 关联图标（如放大镜、定位pin）
            * 周围功能区（如搜索历史、热门推荐）

        3. **生成合理输入**：
        - 根据场景和线索生成3-5个最可能的输入，要求：
            * 符合该场景高频需求（如电商→"蓝牙耳机"）
            * 若界面有推荐词，优先选择同类型的其他发散性的搜索词
            * 长度≤5个汉字或2个英文单词
        - 若坐标非输入框，找到附近的输入框进行判断

        4. **输出格式**（JSON）：{{
        "ui_element": "input_box/button/other",  // 点击的UI元素类型
        "context": "占位符文字或功能描述",       // 关键上下文
        "recommended_queries": ["词1", "词2"],  // 推荐的搜索词
        "confidence": 0-1                      // 置信度(1为最高)
        }}
        - 严格按照输出格式输出，仅仅需要输出 json格式内容，不要带其他任何内容

        当前坐标：x={0}, y={1}（请开始分析）
        """.format(
            x_center, y_center
        )

        answer_content = Qwen25VL72BInstruct(before_img, prompt)
        print(answer_content)
        try:
            result = json.loads(
                answer_content.replace("```", "")
                .replace("json", "")
                .replace("```", "")
                .strip()
            )
            raw_items = result.get("recommended_queries")
            text = random.choice(raw_items)
        except Exception as e:
            print(answer_content)
            print(e)
        self.wxms_page.simple_click_left(x=x_center, y=y_center, fixed=False)
        sleep(1)
        self.wxms_page.simple_paste_text(text=text)
        sleep(1)
        self.wxms_page.simple_press_enter()
        sleep(1)
        return "input " + str(x_center) + " " + str(y_center) + " " + text

    def _handle_scroll(self, element):
        rect = element["rect"]
        x = int(rect[2])
        self.wxms_page.simple_scroll(delta_x=0, delta_y=x)
        sleep(1)
        return "scroll " + str(x)

    def run_one_episode(self, max_steps=100):
        """执行一次最多 max_steps 的游走"""
        self.episode_count += 1
        scroll_flag = False
        for _ in range(max_steps):
            if not self.running:
                break

            # try:
            # Step 1: 截图 before
            before_img, flag = self.take_screenshot_before()
            if not flag:
                continue

            # Step 2: 获取所有可点击区域
            rect_resp, native_rect_resp = (
                self.wxms_page.get_all_elements_rects_with_offset()
            )
            print(rect_resp)
            print(native_rect_resp)
            rects = self.parse_rect_data(rect_resp)
            native_rects = self.parse_rect_data(native_rect_resp)
            print(rects)
            print(native_rects)
            rects = rects + native_rects
            if len(rects) == 0 and scroll_flag is True:
                return

            rects.append(
                {
                    "action": "scroll",
                    "id": "scroll",
                    "rect": [
                        200,
                        200,
                        random.randint(100, 500),
                        random.randint(100, 500),
                    ],
                }
            )
            rects.append(
                {
                    "action": "scroll",
                    "id": "scroll",
                    "rect": [
                        200,
                        200,
                        random.randint(100, 500),
                        random.randint(100, 500),
                    ],
                }
            )

            # Step 3: 随机选择一个 rect 并点击
            # print("请输入rects。", flush=True)
            # ii = input()
            # selected = rects[int(ii.strip())]
            selected = random.choice(rects)

            print(f"正在点击: {selected}")
            if selected["action"] == "click":
                selected_action = self._handle_click(selected)
                scroll_flag = False
            elif selected["action"] == "input":
                selected_action = self._handle_input(selected, before_img)
                scroll_flag = False
            elif selected["action"] == "scroll":
                selected_action = self._handle_scroll(selected)
                scroll_flag = True
            else:
                print(f"不支持的操作类型: {selected['action']}")
                continue

            # Step 4: 截图 after
            after_img, flag = self.take_screenshot_after()
            if not flag:
                continue

            # Step 5: 记录日志
            self.log_step(before_img, selected_action, after_img, selected["rect"])

            self.step_count += 1

            # except Exception as e:
            #     print(f"发生异常: {e}")
            #     self.stop()
            #     return

    def start(self, interval=5):
        """启动 agent，无限循环执行 episode"""
        self.running = True
        self.run_one_episode(max_steps=15)

    def stop(self):
        """停止 agent"""
        self.running = False
        self.wxms_instance.close(self.app_id)
        # data = {
        #     "method": "XWeb.RequestLogout",
        #     "params": {
        #         "role": "server",
        #     }
        # }
        # self.wxms_instance.cdp_proxy(data)
        print("Agent 已经停止")


class MyPage:
    def __init__(self, app_id):
        self.app_id = app_id
        # self.uin = uin
        self.log_data = []
        self.all_screenshots = []

    # def fetch_ilink_auth_code(self):
    #     """发送请求获取 ilink_auth_code"""
    #     url = "http://mmfinderdrsandboxagentsvr.production.polaris:80/v1/sandbox/auth_code"
    #     params = {
    #         "uin": self.uin
    #     }

    #     max_retries = 3
    #     retry_count = 0
    #     print(f"正在请求 auth_code，uin={self.uin}")

    #     while retry_count < max_retries:
    #         try:
    #             response = requests.get(url, params=params, timeout=10)
    #             if response.status_code == 200:
    #                 try:
    #                     data = response.json()
    #                     if data.get("code") == 0 and "data" in data and "ilink_auth_code" in data["data"]:
    #                         auth_code = data["data"]["ilink_auth_code"]
    #                         print("获取 ilink_auth_code 成功:", auth_code)
    #                         return auth_code
    #                     else:
    #                         print("返回数据格式异常，缺少 auth_code")
    #                 except requests.exceptions.JSONDecodeError:
    #                     print("响应不是合法的 JSON 格式")
    #             else:
    #                 print(f"请求失败，状态码: {response.status_code}")
    #         except requests.exceptions.RequestException as e:
    #             print(f"请求出错（第 {retry_count + 1} 次）: {str(e)}")

    #         retry_count += 1
    #         if retry_count < max_retries:
    #             print(f"正在重试第 {retry_count} 次...")
    #             time.sleep(1)  # 等待 1 秒后重试

    #     print("获取 ilink_auth_code 失败，程序将退出")
    #     sys.exit(1)  # 直接退出程序

    def run(self):
        """主运行循环"""
        for i in range(1):
            try:
                self.wxms_instance = wxms.WXMS()
                # self.ilink_auth_code = self.fetch_ilink_auth_code()
                sleep(1)

                # # 在线沙箱
                # data = {
                #     "method": "XWeb.RequestLogin",
                #     "params": {
                #         "role": "server",
                #         "auth_code": self.ilink_auth_code,
                #     }
                # }
                # self.wxms_instance.cdp_proxy(data)
                agent = Agent(wxms_instance=self.wxms_instance, app_id=self.app_id)

                # 运行一个episode
                self.target_id = agent.wxms_page.target_id
                agent.start()

                self.log_data.extend(agent.get_log_data())
                self.all_screenshots.extend(agent.get_screenshots())
                # 切换到新的小程序
                agent.stop()
            except KeyboardInterrupt:
                print("\n检测到中断信号，准备退出...")
                agent.stop()
                break
            # except Exception as e:
            #     print(f"发生异常: {e}")
            #     if 'agent' in locals():
            #         agent.stop()
            #     time.sleep(5)  # 等待后重试
        return self.all_screenshots, self.log_data


def main():
    page = MyPage(app_id="wx3dcca19d0aa51755")

    # 注册 Ctrl+C 退出处理
    def handle_interrupt(signum, frame):
        print("\n检测到 Ctrl+C，准备退出...")
        sys.exit(0)

    signal.signal(signal.SIGINT, handle_interrupt)
    signal.signal(signal.SIGTERM, handle_interrupt)
    # 启动 agent
    page.run()


def handle_task(python_input: PythonInput) -> PythonResult:
    answers = []
    answers_raw_data = []
    label_indexs = []
    screens = []
    screenshots = []
    xmls = []
    xpaths = []

    page = MyPage(app_id=python_input.app_id)
    screenshots, answers = page.run()
    return PythonResult(
        answers=answers if answers else [],
        answers_raw_data=answers_raw_data if answers_raw_data else [],
        interrupt={},
        label_indexs=label_indexs if label_indexs else [],
        screens=screens if screens else [],
        screenshots=screenshots if screenshots else [],
        status=PythonResultStatus.SUCCESS if screenshots else PythonResultStatus.FAILED,
        target_id=page.target_id,
        xmls=xmls if xmls else [],
        xpaths=xpaths if xpaths else [],
    )


# 模拟页面运行
if __name__ == "__main__":
    main()
