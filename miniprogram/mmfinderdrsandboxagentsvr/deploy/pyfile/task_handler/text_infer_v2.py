import base64
import os
import random
import re
import shutil
import sys
import time

import openai
import wxms
from model import PythonInput, PythonResult, PythonResultStatus

headless_mode = int(os.getenv("WXMS_HEADLESS_MODE") or "0")


def sleep(seconds, force=False):
    if headless_mode and not force:
        time.sleep(seconds * 0.0001)
        return
    time.sleep(seconds)


def IsLoadingPage(input_img_path):
    if not os.path.exists(input_img_path):
        return ""
    input_imgs = [input_img_path]
    try:
        client = openai.OpenAI(
            api_key="EMPTY",
            base_url="http://drhttpsvr.polaris:8000/v1/llm-luban-xcx-Qwen2.5-VL-72B-Instruct",
        )
        reasoning_content = ""  # 定义完整思考过程
        answer_content = ""  # 定义完整回复
        is_answering = False  # 判断是否结束思考过程并开始回复

        loading_page_judge_promot = """图片展示的是一个微信小程序的页面截图，请判断该页面是否加载完成，如果页面已加载完成，请回复是，如果页面正在加载，请回答否。
        注意：1、只需要回答是或者否，不要有任何其他多余的回复 2、搜索结果为空的页面认为是加载完成的 3、如果页面出现白屏，则没有加载完成，回复否"""

        input_imgs_content = [
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_to_base64(img)}"},
            }
            for img in input_imgs
        ]
        messages = [
            {
                "role": "user",
                "content": input_imgs_content
                + [{"type": "text", "text": loading_page_judge_promot}],
            }
        ]
        # print('vl: ', len(messages[0]['content']))
        completion = client.chat.completions.create(
            model="llm-luban-xcx-Qwen2.5-VL-72B-Instruct",
            messages=messages,
            temperature=0.0,
            seed=2025,
            stream=True,
        )
        for chunk in completion:
            # 处理usage信息
            if not getattr(chunk, "choices", None):
                print("\n" + "=" * 20 + "Token 使用情况" + "=" * 20 + "\n")
                print(chunk.usage)
                continue

            delta = chunk.choices[0].delta

            # 处理空内容情况
            if not getattr(delta, "reasoning_content", None) and not getattr(
                delta, "content", None
            ):
                continue

            # 处理开始回答的情况
            if not getattr(delta, "reasoning_content", None) and not is_answering:
                # print("\n" + "=" * 20 + "完整回复" + "=" * 20 + "\n")
                is_answering = True

            # 处理思考过程
            if getattr(delta, "reasoning_content", None):
                # print(delta.reasoning_content, end='', flush=True)
                reasoning_content += delta.reasoning_content
            # 处理回复内容
            elif getattr(delta, "content", None):
                # print(delta.content, end='', flush=True)
                answer_content += delta.content
        return answer_content

    except Exception as e:
        print(f"捕获到异常: {str(e)}", file=sys.stderr)
        return ""


def IsLoadingPageQwen7B(input_img_path, wait_model_base_url, wait_model_name):
    if not os.path.exists(input_img_path):
        return ""
    input_imgs = [input_img_path]
    base_url = wait_model_base_url
    model_name = wait_model_name
    try:
        client = openai.OpenAI(
            api_key="EMPTY",
            base_url=base_url,
        )
        reasoning_content = ""  # 定义完整思考过程
        answer_content = ""  # 定义完整回复
        is_answering = False  # 判断是否结束思考过程并开始回复

        loading_page_judge_promot = """图片展示的是一个微信小程序的页面截图，请判断该页面是否加载完全，如果页面已加载完全，请回复是，如果页面没有加载完全，请回答否。 
        注意：1、回复需要遵从格式<think>...</think> <answer>...</answer>,其中<think>标签中是判断的理由和推理过程，<answer>标签中是判断结果，即“是”或者“否” 
        2、页面出现大片空白区域，认为没有加载完全，回复否；但是页面有搜索框的搜索结果为空，或者订单页面订单查询结果为无时，认为是加载完全的，回复是 
        3、如果页面出现白屏，则没有加载完成，回复否
        4、如果页面有[跳过]字样，说明是开屏广告，没有加载完成，回复否
        5、如果页面有[正在]字样，说明正在加载，没有加载完成，回复否"""

        input_imgs_content = [
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_to_base64(img)}"},
            }
            for img in input_imgs
        ]
        messages = [
            {
                "role": "user",
                "content": input_imgs_content
                + [{"type": "text", "text": loading_page_judge_promot}],
            }
        ]

        # print("messages ", messages)
        completion = client.chat.completions.create(
            model=model_name,
            messages=messages,
            temperature=0.0,
            seed=2025,
            stream=True,
        )
        for chunk in completion:
            # 处理usage信息
            if not getattr(chunk, "choices", None):
                print("\n" + "=" * 20 + "Token 使用情况" + "=" * 20 + "\n")
                print(chunk.usage)
                continue

            delta = chunk.choices[0].delta

            # 处理空内容情况
            if not getattr(delta, "reasoning_content", None) and not getattr(
                delta, "content", None
            ):
                continue

            # 处理开始回答的情况
            if not getattr(delta, "reasoning_content", None) and not is_answering:
                # print("\n" + "=" * 20 + "完整回复" + "=" * 20 + "\n")
                is_answering = True

            # 处理思考过程
            if getattr(delta, "reasoning_content", None):
                # print(delta.reasoning_content, end='', flush=True)
                reasoning_content += delta.reasoning_content
            # 处理回复内容
            elif getattr(delta, "content", None):
                # print(delta.content, end='', flush=True)
                answer_content += delta.content
        print("wait_answer_content ", answer_content)

        def parse_waitmodel_output(content):
            final_answer = ""
            answer_match = re.search(r"<answer>(.*?)</answer>", content, re.DOTALL)
            if answer_match:
                final_answer = answer_match.group(1).strip()

            return final_answer.strip()

        return parse_waitmodel_output(answer_content)

    except Exception as e:
        print(e)
        return ""


def simple_screenshot(
    _wxms_page,
    path,
    use_wait_model="1",
    wait_model_base_url="http://*************:8000/v1/",
    wait_model_name="eval_xiaodezhang-waitmodel_529_llm_luban_minip_waitmodelduphard_xiaode_ck200_export-0529-19",
):
    time.sleep(0.5)
    if use_wait_model == "0":
        if headless_mode:
            time.sleep(2)
        return _wxms_page.simple_screenshot(path)

    for i in range(3):
        # time.sleep(0.7)
        _wxms_page.simple_screenshot(path)
        page_loading_finished = IsLoadingPageQwen7B(
            path, wait_model_base_url, wait_model_name
        )  # IsLoadingPage(path)
        print("xiaodezhang_screen_shoot ", page_loading_finished, path)
        if page_loading_finished == "是":
            return 0
        else:
            if i < 2:
                try:
                    shutil.copyfile(
                        path, path.split(".")[0] + "_loading_{}.jpg".format(i)
                    )
                except:
                    print("rename {} error", path)


def __get_and_create_screenshot_dir() -> str:
    root_folder = f"{os.path.dirname(__file__)}/outputs"
    folder_name = os.path.basename(__file__).replace(".py", "")
    timestamp = time.strftime("%Y%m%d_%H%M%S_%f")[:-3]
    random_id = "".join(random.sample("0123456789abcdef", 8))
    screenshot_dir = (
        f"{root_folder}/{folder_name}/cache_screenshot_{timestamp}_{random_id}"
    )
    os.makedirs(screenshot_dir)
    return screenshot_dir


def image_to_base64(image_path):
    try:
        with open(image_path, "rb") as image_file:
            encoded_string = base64.b64encode(image_file.read())
            return encoded_string.decode("utf-8")
    except FileNotFoundError:
        print(f"错误：未找到指定的图片文件: {image_path}", file=sys.stderr)
    except Exception as e:
        print(f"错误：发生了未知错误: {str(e)}", file=sys.stderr)
        print(f"错误图片路径: {image_path}", file=sys.stderr)


def Qwen25VL72BInstruct(pre_imgs, vl_prompt, instruction):
    try:
        client = openai.OpenAI(
            api_key="EMPTY",
            base_url="http://drhttpsvr.polaris:8000/v1/llm-luban-xcx-Qwen2.5-VL-72B-Instruct",
        )
        reasoning_content = ""  # 定义完整思考过程
        answer_content = ""  # 定义完整回复
        is_answering = False  # 判断是否结束思考过程并开始回复
        input_imgs = pre_imgs[-8:]

        input_imgs_content = [
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_to_base64(img)}"},
            }
            for img in input_imgs
        ]
        messages = [
            {
                "role": "user",
                "content": input_imgs_content
                + [{"type": "text", "text": vl_prompt.format(instruction)}],
            }
        ]
        # print('vl: ', len(messages[0]['content']))
        completion = client.chat.completions.create(
            model="llm-luban-xcx-Qwen2.5-VL-72B-Instruct",
            messages=messages,
            temperature=0.0,
            seed=2025,
            stream=True,
        )
        for chunk in completion:
            # 处理usage信息
            if not getattr(chunk, "choices", None):
                print("\n" + "=" * 20 + "Token 使用情况" + "=" * 20 + "\n")
                print(chunk.usage)
                continue

            delta = chunk.choices[0].delta

            # 处理空内容情况
            if not getattr(delta, "reasoning_content", None) and not getattr(
                delta, "content", None
            ):
                continue

            # 处理开始回答的情况
            if not getattr(delta, "reasoning_content", None) and not is_answering:
                # print("\n" + "=" * 20 + "完整回复" + "=" * 20 + "\n")
                is_answering = True

            # 处理思考过程
            if getattr(delta, "reasoning_content", None):
                # print(delta.reasoning_content, end='', flush=True)
                reasoning_content += delta.reasoning_content
            # 处理回复内容
            elif getattr(delta, "content", None):
                # print(delta.content, end='', flush=True)
                answer_content += delta.content
        return answer_content

    except Exception as e:
        print(f"捕获到异常: {str(e)}", file=sys.stderr)
        return ""


def Qwen25VL7BInstruct(pre_imgs, vl_prompt, instruction, base_url, model_name):
    try:
        client = openai.OpenAI(
            api_key="EMPTY",
            base_url=base_url,
        )
        reasoning_content = ""  # 定义完整思考过程
        answer_content = ""  # 定义完整回复
        is_answering = False  # 判断是否结束思考过程并开始回复
        input_imgs = pre_imgs[-1:]

        input_imgs_content = [
            {
                "type": "image_url",
                "image_url": {"url": f"data:image/jpeg;base64,{image_to_base64(img)}"},
            }
            for img in input_imgs
        ]
        messages = [
            {
                "role": "user",
                "content": input_imgs_content
                + [{"type": "text", "text": vl_prompt.format(instruction)}],
            }
        ]
        # print('vl: ', len(messages[0]['content']))
        completion = client.chat.completions.create(
            model=model_name,
            messages=messages,
            temperature=0.0,
            seed=2025,
            stream=True,
        )
        for chunk in completion:
            # 处理usage信息
            if not getattr(chunk, "choices", None):
                print("\n" + "=" * 20 + "Token 使用情况" + "=" * 20 + "\n")
                print(chunk.usage)
                continue

            delta = chunk.choices[0].delta

            # 处理空内容情况
            if not getattr(delta, "reasoning_content", None) and not getattr(
                delta, "content", None
            ):
                continue

            # 处理开始回答的情况
            if not getattr(delta, "reasoning_content", None) and not is_answering:
                # print("\n" + "=" * 20 + "完整回复" + "=" * 20 + "\n")
                is_answering = True

            # 处理思考过程
            if getattr(delta, "reasoning_content", None):
                # print(delta.reasoning_content, end='', flush=True)
                reasoning_content += delta.reasoning_content
            # 处理回复内容
            elif getattr(delta, "content", None):
                # print(delta.content, end='', flush=True)
                answer_content += delta.content
        return answer_content

    except Exception as e:
        print(e)
        return ""


def Qwen25_7B_vl(python_input, vl_prompt):
    app_id = python_input.app_id
    python_input.app_name
    base_url = python_input.vlt_base_url
    model_name = python_input.vlt_model_name
    instruction = python_input.instruction
    prompt = python_input.prompt_vlt
    use_wait_model = python_input.use_wait_model
    wait_model_base_url = python_input.wait_model_base_url
    wait_model_name = python_input.wait_model_name

    try:
        all_screenshots = []  # 用于存储所有截图路径
        all_answers = []
        all_answers_raw_data = []
        wxms_instance = wxms.WXMS()
        sleep(1)

        # # 在线沙箱
        # print("请输入token。", flush=True)
        # a = input()
        # # a = "1_BgAASuFhysycGU/ya7vFfdNUMwmTzH/qc4gWmrsuOzfrBDUKPAAylnu4dTNZV6N/g7VULjWg6OqDoiuV/sI3KnVD26Ix8cwQNKkC5rzEbtYPYXHD7eqgMiirfQY+h4JF"
        # data = {
        #     "method": "XWeb.RequestLogin",
        #     "params": {
        #         "role": "server",
        #         "auth_code": a,
        #     }
        # }
        # wxms_instance.cdp_proxy(data)

        wxms_page = wxms_instance.simple_get_app(app_id, close_old=True)
        sleep(1)
        screenshot_dir = __get_and_create_screenshot_dir()
        screenshot_name = f"{screenshot_dir}/init.jpg"
        simple_screenshot(
            wxms_page,
            screenshot_name,
            use_wait_model,
            wait_model_base_url,
            wait_model_name,
        )
        wxms_page.dom_enable()
        sleep(1)
        all_screenshots.append(screenshot_name)

        pre_imgs = [screenshot_name]

        step_i = 0

        for turn_index in range(15):
            # if before_step_summary == "":
            #     before_step_summary_input = "无"
            # else:
            #     before_step_summary_input = before_step_summary

            # xml_content = wxms_page.get_dom_xml()
            # if (xml_content.get('code') != 0 and xml_content.get('ret') != 0):
            #     return None
            # try:
            #     xml_data_former = xml_content['data']['result']['result']['value']
            # except KeyError as e:
            #     sleep(5)
            #     continue

            # xml_data_former_tmp = xml_content['data']['result']['result']['value']

            # if '<native>' not in xml_data_former:
            #     xml_data_former = xml_data_former.replace("<page>","").replace("</page>","").replace("<iframe>","").replace("</iframe>","")
            #     #print('####xml_data_former trans:\n', xml_data_former)
            # else:
            #     xml_data_former = add_attributes_to_tabbar_items(xml_data_former)
            #     #print('####xml_data_former trans:\n', xml_data_former)

            # #print(xml_data_former)
            # xml = wxconvert(xml_data_former)
            # root = ET.fromstring(xml)
            # _, elements = process_accessibility_tree(root)
            # screen, xpath = parameterize_actions(elements, root)
            # index_to_xpath = {path:idx for idx, path in xpath.items()}
            # result = {
            # "screen": screen,
            # "indexToXpath": xpath,
            # "xpathToIndex": index_to_xpath
            # }
            # #print(result)

            answer_content = Qwen25VL7BInstruct(
                pre_imgs, prompt, instruction, base_url, model_name
            )
            all_answers_raw_data.append(answer_content)
            # print('#### answer_content:', answer_content)

            match = re.search(r"<answer>(.*?)</answer>", answer_content, re.DOTALL)
            if match:
                action_7b = match.group(1)  # 提取第一个捕获组中的内容
            else:
                action_7b = ""

            match_think = re.search(r"<think>(.*?)</think>", answer_content, re.DOTALL)
            if match_think:
                match_think.group(1)
            else:
                pass

            action_7b = action_7b.replace("\n", "")

            # print("\n -----Here is 7b answer_content------\n", action_7b, think_content)

            # print('##############')
            # print(action_7b, pre_action)
            # print('##############')

            if False:  # action_7b == pre_action:  #模型走不出来了走vl
                answer_content = Qwen25VL72BInstruct(pre_imgs, vl_prompt, instruction)
                all_answers_raw_data.append(answer_content)
                match = re.search(r"<answer>(.*?)</answer>", answer_content, re.DOTALL)
                re.search(r"<summary>(.*?)</summary>", answer_content, re.DOTALL)
                if match:
                    action = match.group(1)  # 提取第一个捕获组中的内容
                else:
                    action = ""
                # step_summary_vl = ""
                # if match_summary:
                #     step_summary_vl = match_summary.group(1)

                # print("xxxxxxxxxxxxxxxxxxxxxxzpw")
                # before_step_summary += str(step_i + 1) + ":" + step_summary_vl + ";"
                # print("before_step_summary: ", before_step_summary)
                step_i = step_i + 1
                # print("xxxxxxxxxxxxxxxxxxxxxxzpw")

                action = action.replace("\n", "")
                # print('##vl action:', action)
                ###执行沙箱
                # try:
                if action.startswith("click"):
                    parts = action.replace(",", " ").split()
                    x = int(parts[1])
                    y = int(parts[2])
                    wxms_page.simple_click_left(x=x, y=y)
                    sleep(1)
                    screenshot_name = "{}/click_vl_{}.jpg".format(
                        screenshot_dir, turn_index
                    )
                    simple_screenshot(
                        wxms_page,
                        screenshot_name,
                        use_wait_model,
                        wait_model_base_url,
                        wait_model_name,
                    )

                elif action.startswith("input"):
                    parts = action.replace(",", " ").split()
                    x = int(parts[1])
                    y = int(parts[2])
                    text = parts[3]
                    wxms_page.simple_click_left(x=x, y=y)
                    sleep(1)
                    wxms_page.simple_paste_text(text=text)
                    sleep(1)
                    wxms_page.simple_press_enter()
                    sleep(1)
                    screenshot_name = "{}/input_vl_{}.jpg".format(
                        screenshot_dir, turn_index
                    )
                    simple_screenshot(
                        wxms_page,
                        screenshot_name,
                        use_wait_model,
                        wait_model_base_url,
                        wait_model_name,
                    )

                elif action.startswith("scroll"):
                    x = int(action.split(" ", 1)[1])
                    wxms_page.simple_scroll(delta_y=x)
                    sleep(1 * 5)
                    screenshot_name = "{}/scroll_vl_{}.jpg".format(
                        screenshot_dir, turn_index
                    )
                    simple_screenshot(
                        wxms_page,
                        screenshot_name,
                        use_wait_model,
                        wait_model_base_url,
                        wait_model_name,
                    )
                elif action == "finish" or action == "stop":
                    sleep(1)
                    screenshot_name = "{}/{}_vl_{}.jpg".format(
                        screenshot_dir, action, turn_index
                    )
                    simple_screenshot(
                        wxms_page,
                        screenshot_name,
                        use_wait_model,
                        wait_model_base_url,
                        wait_model_name,
                    )
                    all_answers.append(action)
                    # all_screens.append(screen)
                    # all_label_index.append(-1)
                    # all_xmls.append(xml_data_former)
                    # all_xpaths.append(result['indexToXpath'])
                    break
                elif action.startswith("wait"):
                    sleep(3)
                    screenshot_name = "{}/{}_vl_{}.jpg".format(
                        screenshot_dir, action, turn_index
                    )
                    simple_screenshot(
                        wxms_page,
                        screenshot_name,
                        use_wait_model,
                        wait_model_base_url,
                        wait_model_name,
                    )
                # except Exception as e:
                #     print('vl 执行失败')
                all_screenshots.append(screenshot_name)
                all_answers.append(action)
                # all_screens.append(screen)
                # all_label_index.append(-1)
                # all_xmls.append(xml_data_former)
                # all_xpaths.append(result['indexToXpath'])

                # pre_screen = result['screen']
                # pre_xpath = result["indexToXpath"]
                pre_imgs.append(screenshot_name)

            else:  ##继续走文本
                if action_7b.startswith("click"):
                    parts = action_7b.replace(",", " ").split()
                    x = int(parts[1])
                    y = int(parts[2])
                    wxms_page.simple_click_left(x=x, y=y)
                    sleep(3)
                    screenshot_name = "{}/click_vlt_{}.jpg".format(
                        screenshot_dir, turn_index
                    )
                    simple_screenshot(
                        wxms_page,
                        screenshot_name,
                        use_wait_model,
                        wait_model_base_url,
                        wait_model_name,
                    )

                elif action_7b.startswith("input"):
                    parts = action_7b.replace(",", " ").split()
                    x = int(parts[1])
                    y = int(parts[2])
                    text = parts[3]
                    wxms_page.simple_click_left(x=x, y=y)
                    sleep(3)
                    wxms_page.simple_paste_text(text=text)
                    sleep(3)
                    wxms_page.simple_press_enter()
                    sleep(3)
                    screenshot_name = "{}/input_vlt_{}.jpg".format(
                        screenshot_dir, turn_index
                    )
                    simple_screenshot(
                        wxms_page,
                        screenshot_name,
                        use_wait_model,
                        wait_model_base_url,
                        wait_model_name,
                    )

                elif action_7b.startswith("scroll"):
                    x = int(action_7b.split(" ", 1)[1])
                    wxms_page.simple_scroll(delta_y=x)
                    sleep(1 * 5)
                    screenshot_name = "{}/scroll_vlt_{}.jpg".format(
                        screenshot_dir, turn_index
                    )
                    simple_screenshot(
                        wxms_page,
                        screenshot_name,
                        use_wait_model,
                        wait_model_base_url,
                        wait_model_name,
                    )
                elif action_7b == "finish" or action_7b == "stop":
                    sleep(1)
                    screenshot_name = "{}/{}_vlt_{}.jpg".format(
                        screenshot_dir, action_7b, turn_index
                    )
                    simple_screenshot(
                        wxms_page,
                        screenshot_name,
                        use_wait_model,
                        wait_model_base_url,
                        wait_model_name,
                    )
                    all_answers.append(action_7b)
                    # all_screens.append(screen)
                    # all_label_index.append(-1)
                    # all_xmls.append(xml_data_former)
                    # all_xpaths.append(result['indexToXpath'])
                    break
                elif action_7b.startswith("wait"):
                    sleep(3)
                    screenshot_name = "{}/{}_vlt_{}.jpg".format(
                        screenshot_dir, action_7b, turn_index
                    )
                    simple_screenshot(
                        wxms_page,
                        screenshot_name,
                        use_wait_model,
                        wait_model_base_url,
                        wait_model_name,
                    )
                # except Exception as e:
                #     print('vl 执行失败')
                all_screenshots.append(screenshot_name)
                all_answers.append(action_7b)
                # all_screens.append(screen)
                # all_label_index.append(-1)
                # all_xmls.append(xml_data_former)
                # all_xpaths.append(result['indexToXpath'])

                # pre_screen = result['screen']
                # pre_xpath = result["indexToXpath"]
                pre_imgs.append(screenshot_name)
        wxms_instance.close(app_id)
        # data = {
        #     "method": "XWeb.RequestLogout",
        #     "params": {
        #         "role": "server",
        #     }
        # }
        # wxms_instance.cdp_proxy(data)
        return (
            all_screenshots,
            all_answers,
            all_answers_raw_data,
            wxms_page.target_id,
            {},
        )
    except wxms.InterruptError as e:
        wxms_instance.close(app_id, with_card=False)
        return (
            all_screenshots,
            all_answers,
            all_answers_raw_data,
            wxms_page.target_id,
            e.params,
        )
    except Exception as e:
        print(e)
        wxms_instance.close(app_id)
        # data = {
        #     "method": "XWeb.RequestLogout",
        #     "params": {
        #         "role": "server",
        #     }
        # }
        # wxms_instance.cdp_proxy(data)
        return (
            all_screenshots,
            all_answers,
            all_answers_raw_data,
            wxms_page.target_id,
            {},
        )


def handle_task(python_input: PythonInput) -> PythonResult:
    answers = []
    answers_raw_data = []
    interrupt = {}
    label_indexs = []
    screens = []
    screenshots = []
    target_id = ""
    xmls = []
    xpaths = []
    vl_prompt = """
    你是一个小程序操作助手，上述图像是按顺序操作任务指令，最近一张是当前图像。
    当前指令: 如果页面有“点击添加小程序下次尽快找到”的弹窗，一定要优先关闭弹窗,如果指令提到地址和当前地址不对一定要切换地址，如果当前页面能够搜索优先使用搜索功能来实现指令，{}，请问在当前图像里我要怎么操作？当前只能有一种输出选项：只能回复接下来的一个动作，不能回复多个动作，并给出在页面的精确像素坐标（x y），最终输出格式严格遵循<思考>xxx</思考><answer>xxx</answer><summary>xxx</summary>这个格式。answer动作只能在以下6 类集合选择：1. click x y(代表点击像素坐标 x,y)，2. input x y t（代表在x y 选中输入框并输入文本t） 3. finish（代表任务成功结束） 4. stop（代表任务无法进行 进程终止）5. scroll x（代表下滑x个像素，x为负数代表上滑|x|个像素）6. wait t（代表当前页面处于加载中，可以等待t秒）。 思考内容：首先通过对话上下文判断上一步的操作是否生效，如果上一步操作无效，需要反思并采取正确的动作，例如 1.  input x y t 后页面是否有显示 input 的内容t，没有的话则操作未生效，考虑网页不支持本步input 而应该采取click x y 操作使用点击小程序拉起的键盘输入 2. click x y 后操作未生效考虑点击位置无效，应该采取点击其他位置click x1 y1实现指令 3. assistant重复输入相同命令表示当前操作无效，需要采取其他方法实现指令 4. 需要详细检查 input 的内容 t 是否正确显示在页面中 5. 最近两张图像没有变化时需要反思动作是否正确
    """
    screenshots, answers, answers_raw_data, target_id, interrupt = Qwen25_7B_vl(
        python_input, vl_prompt
    )
    # label_indexs = [str(label_index) for label_index in label_indexs]
    # xpaths = [json.dumps(xpath) for xpath in xpaths]
    screens = []
    label_indexs = []
    xpaths = []
    xmls = []
    return PythonResult(
        answers=answers if answers else [],
        answers_raw_data=answers_raw_data if answers_raw_data else [],
        interrupt=interrupt,
        label_indexs=label_indexs if label_indexs else [],
        screens=screens if screens else [],
        screenshots=screenshots if screenshots else [],
        status=PythonResultStatus.SUCCESS if screenshots else PythonResultStatus.FAILED,
        target_id=target_id if target_id else "",
        xmls=xmls if xmls else [],
        xpaths=xpaths if xpaths else [],
    )
