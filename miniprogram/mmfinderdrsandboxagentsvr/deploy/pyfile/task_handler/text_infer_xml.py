import argparse
import base64
import json
import os
import re
import sys
import time
import openai
import wxms
import xml.etree.ElementTree as ET

from jinja2 import Template
from model import PythonIn<PERSON>, PythonResult, PythonResultStatus
from util.UIFormer_WX.convert_wechat_minihtml_to_xml import convert as wxconvert
from util.UIFormer_WX.simplify_xml import (
    process_accessibility_tree,
    parameterize_actions,
    add_attributes_to_tabbar_items,
)


headless_mode = int(os.getenv("WXMS_HEADLESS_MODE") or "0")


def sleep(seconds, force=False):
    time.sleep(seconds)


def simple_screenshot(_wxms_page, path):
    if headless_mode:
        time.sleep(1)
    _wxms_page.simple_screenshot(path)
    time.sleep(0.3)
    return


def clean_text(input_text):
    return input_text
    """
    处理输入文本，提取[数字]和括号内容，忽略括号内为空的条目
    
    参数:
    input_text (str): 输入的原始文本
    
    返回:
    str: 处理后的文本
    """
    # 定义正则表达式模式
    pattern = r"\[(\d+)\][^\(]*\(([^)]*)\)"

    # 查找所有匹配项
    matches = re.findall(pattern, input_text)

    # 构建结果字符串，过滤掉括号内为空的条目
    result = []
    for match in matches:
        number = match[0]
        content = match[1].strip()
        # 只添加括号内内容不为空的条目
        if content:
            result.append(f"[{number}] ({content})")

    # 用换行符连接结果
    return "\n".join(result)


def parse_xml(wxms_page):
    try:
        xml_content = wxms_page.get_dom_xml()
        if xml_content.get("code") != 0 and xml_content.get("ret") != 0:
            return {"xml_content": xml_content}
        xml_info = xml_content["data"]["result"]["result"]
        xml_data = xml_info["value"]
        xml_data = (
            xml_data.replace("<page>", "").replace("</page>", "").replace("<iframe>", "").replace("</iframe>", "")
        )
        xml_data = add_attributes_to_tabbar_items(xml_data)
        xml = wxconvert(xml_data)
        root = ET.fromstring(xml)
        node_ungrouped, elements = process_accessibility_tree(root)
        screen, xpath = parameterize_actions(elements, root)
        screen = clean_text(re.sub(r"resource_id: \d+; ", "", screen))

        index_to_xpath = {path: idx for idx, path in xpath.items()}
        result = {"xml_data": xml_data, "screen": screen, "indexToXpath": xpath, "xpathToIndex": index_to_xpath}
        return result
    except Exception as e:
        print("parse_xml error", e)
        return {"xml_data": "", "screen": "", "indexToXpath": "", "xpathToIndex": ""}


def parse_action(message):
    tool_calls = message.get("tool_calls", [])
    actions = []
    for tool_call in tool_calls:
        function = tool_call["function"]
        name = function["name"]
        args = json.loads(function["arguments"])
        actions.append({"name": name, "args": args})
    return actions


def action_to_wxms(action, wxms_page, indexToXpath):

    name = action["name"]
    args = action["args"]
    if name == "terminate":
        return
    if name == "wait":
        time.sleep(args["seconds"])
        return
    elif name == "scroll":
        delta_x = delta_y = 0
        direction = args.get("direction")
        if direction == "up":
            delta_y = -300
        elif direction == "down":
            delta_y = 300
        elif direction == "left":
            delta_x = -200
        elif direction == "right":
            delta_x = 200
        wxms_page.simple_scroll(delta_x, delta_y)
        return
    xpath = indexToXpath.get(args["id"])
    if not xpath:
        return "无效的元素id"

    wxms_page.simple_click_left_by_xpath(xpath)
    sleep(1)
    if name == "type":
        content = args["content"]
        wxms_page.simple_paste_text(text=content)
        sleep(0.5)
        wxms_page.simple_press_enter()
    return "success"


tools = [
    {
        "type": "function",
        "function": {
            "name": "click",
            "description": "点击对应的元素",
            "parameters": {
                "type": "object",
                "properties": {"id": {"type": "integer", "description": "需要点击的元素id"}},
                "required": ["id"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "type",
            "description": "在对应元素中输入内容",
            "parameters": {
                "type": "object",
                "properties": {
                    "id": {"type": "integer", "description": "元素id"},
                    "content": {"type": "string", "description": "输入的内容"},
                },
                "required": ["id", "content"],
            },
        },
    },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "scroll",
    #         "description": "滑动屏幕",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "direction": {
    #                     "type": "string",
    #                     "enum": ["up", "down", "left", "right"],
    #                     "description": "向指定的的方向滑动屏幕，比如down代表将屏幕向下滑动，展示屏幕上方的隐藏内容"
    #                 },
    #             },
    #             "required": ["direction"]
    #         }
    #     }
    # },
    {
        "type": "function",
        "function": {
            "name": "wait",
            "description": "等待",
            "parameters": {
                "type": "object",
                "properties": {
                    "seconds": {"type": "integer", "description": "等待时间"},
                },
                "required": ["seconds"],
            },
        },
    },
    {
        "type": "function",
        "function": {
            "name": "terminate",
            "description": "结束小程序操作",
            "parameters": {
                "type": "object",
                "properties": {
                    "state": {"type": "string", "enum": ["success", "failure"], "description": "任务完成状态"},
                    "final_answer": {"type": "string", "description": "展示给用户的最终答复"},
                },
                "required": ["state"],
            },
        },
    },
]


def run(app_id, app_name, base_url, model_name, instruction, max_turn=30):
    system = """你是一个小程序操作助手，现在需要操作小程序来完成用户的需求，整个过程可能需要进行多步的操作和尝试才能完成
请尽可能的进行尝试和探索来完成用户的需求

[[输入信息]]
- 用户输入：任务指令或者补充信息，将会包含在<user_input></user_input>标签中
- 小程序状态：根据小程序页面的dom树进行解析和简化后的结构化文本，将会包含在<state_i></state_i>标签中（代表当前操作第i步）
  - 每个可操作的元素开头都会有一个唯一的id标识在[]内（比如`[n]`代表当前元素的id是n）
  - clickable代表元素可进行点击；typeable代表元素可以进行输入；scrollable代表元素可进行滑动

[[任务]]
1. 你需要以完成用户的需求为主要目标，对当前小程序的状态进行分析，并从tools中选择合适的tool对小程序进行操作
2. 优先使用搜索能力来查找需要的内容
  - 有的搜索框不会标注typeable，只会有clickable，你需要根据text的内容（比如'搜xx'）进行合理的推断，判断是否可以点击进行搜索
3. 每次操作完成后，你将会收到最新的小程序状态信息
4. 整个操作过程需要反复迭代和尝试，直到任务完成
5. 点单任务必须要处理到支付操作才算任务完成

[[注意事项]]
- 你只对最新的state中展示的元素编号进行操作
- 如果当页面元素中出现有关：*服务使用条款*、*隐私协议*等信息时，**点击上层/前面的同意按钮**（注意不是条款内容），否则其他的操作（登录、支付等）可能无法进行点击
- 如果发现无法完成任务，转变思路再尝试一下
- 由于一些不可抗力，有些元素会丢失掉text信息
- 页面状态为空时，wait一段时间后再操作
- 不要擅自篡改或简化用户的需求
- 查询天气时可尝试点击地点来切换位置

**请将当前任务状态的总结以及下一步的规划输出在<think></think>标签内，字数小于150字**"""

    template = Template(
        """<user_input>
{{instruction}}
</user_input>

<state_0>
{{state}}
</state_0>
"""
    )
    client = openai.OpenAI(
        api_key="EMPTY",
        base_url=base_url,
    )

    wxms_instance = wxms.WXMS()
    try:
        all_screenshots = []  # 用于存储所有截图路径
        all_answers = []
        all_answers_raw_data = []
        time.sleep(1)
        wxms_page = wxms_instance.simple_get_app(app_id, close_old=True)
        time.sleep(1)
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        screenshot_dir = f"./outputs/cache_screenshot_{timestamp}"
        os.makedirs(screenshot_dir, exist_ok=True)
        screenshot_name = f"{screenshot_dir}/init.jpg"
        simple_screenshot(wxms_page, screenshot_name)
        wxms_page.dom_enable()
        time.sleep(1)
        abs_path = os.path.abspath(screenshot_name)
        all_screenshots.append(abs_path)
        xml_info = parse_xml(wxms_page)
        xml_list = [xml_info]

        messages = [
            {"role": "system", "content": system},
            {"role": "user", "content": template.render(instruction=instruction, state=xml_info["screen"])},
        ]
        finished = False
        pre_action = {}
        for turn_index in range(1, max_turn + 2):
            if "no_think" in model_name:
                messages[-1]["content"] += " /no_think"
                gen_args = {"temperature": 0.6, "top_p": 0.8, "extra_body": {"top_k": 20, "min_p": 0.0}}
            else:
                gen_args = {"temperature": 0.7, "top_p": 0.95, "extra_body": {"top_k": 20, "min_p": 0.0}}
            completion = client.chat.completions.create(
                model=model_name.replace("_no_think", ""),
                messages=messages,
                stream=False,
                max_tokens=1024,
                tool_choice="auto",
                tools=tools,
                **gen_args,
            )

            assistant_message = completion.choices[0].message.model_dump()
            answer = assistant_message["content"]
            all_answers_raw_data.append(answer)
            actions = parse_action(assistant_message)
            messages.append(assistant_message)

            for action in actions:
                all_answers.append(action)
                if action.get("name") == "terminate":
                    finished = True
                    break
                state = action_to_wxms(action, wxms_page, xml_info["indexToXpath"])
                if pre_action and pre_action["name"] == "type":
                    sleep(4)
                if state == "success":
                    screenshot_name = f"{screenshot_dir}/{turn_index}_{action['name']}.jpg"
                    simple_screenshot(wxms_page, screenshot_name)
                    abs_path = os.path.abspath(screenshot_name)
                    all_screenshots.append(abs_path)
                    xml_info = parse_xml(wxms_page)
                    xml_list.append(xml_info)
                    messages.append(
                        {
                            "role": "tool",
                            "content": f"<state_{turn_index}>\n{xml_info['screen']}\n</state_{turn_index}>",
                        }
                    )
                else:
                    messages.append(
                        {"role": "tool", "content": f"<state_{turn_index}>\n{state}\n</state_{turn_index}>\n"}
                    )
                pre_action = action
            if finished:
                break
        if not finished:
            all_answers.append(
                {"name": "terminate", "args": {"state": "failure", "final_answer": "操作失败，已达到最大操作次数"}}
            )
        wxms_instance.close(app_id)
        return all_screenshots, all_answers, all_answers_raw_data, wxms_page.target_id, [x["screen"] for x in xml_list]
    except wxms.InterruptError as e:
        wxms_instance.close(app_id, with_card=False)
        if all_answers and all_answers[-1]["name"] != "terminate":
            all_answers.append({"name": "terminate", "args": {"state": "failure", "final_answer": "操作被中断"}})
        return all_screenshots, all_answers, all_answers_raw_data, wxms_page.target_id, [x["screen"] for x in xml_list]
    except Exception as e:
        wxms_instance.close(app_id)
        if all_answers and all_answers[-1]["name"] != "terminate":
            all_answers.append(
                {"name": "terminate", "args": {"state": "failure", "final_answer": "操作失败，程序意外退出"}}
            )
        print("main loop error", e)
        return all_screenshots, all_answers, all_answers_raw_data, wxms_page.target_id, [x["screen"] for x in xml_list]
    finally:
        json.dump(messages, open("./xml_messages_log.json", "w"))
        json.dump(xml_list, open("./xml_info_list.json", "w"))


def handle_task(python_input: PythonInput) -> PythonResult:
    answers = []
    answers_raw_data = []
    label_indexs = []
    screens = []
    screenshots = []
    target_id = ""
    xmls = []
    xpaths = []
    interrupt = {}
    try:
        screenshots, answers, answers_raw_data, target_id, screens = run(
            python_input.app_id,
            python_input.app_name,
            python_input.base_url,
            python_input.model_name,
            python_input.instruction,
        )
    except Exception as e:
        print("run_agent error", e)
    return PythonResult(
        answers=[json.dumps(a, ensure_ascii=False) for a in answers] if answers else [],
        answers_raw_data=answers_raw_data if answers_raw_data else [],
        interrupt=interrupt,
        label_indexs=label_indexs if label_indexs else [],
        screens=screens if screens else [],
        screenshots=screenshots if screenshots else [],
        status=PythonResultStatus.SUCCESS if screenshots else PythonResultStatus.FAILED,
        target_id=target_id if target_id else "",
        xmls=xmls if xmls else [],
        xpaths=xpaths if xpaths else [],
    )


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="")
    parser.add_argument("--app_id", help="appid", default="wxc30ae3bc7fb4cab1")
    parser.add_argument("--app_name", help="appname", default="墨迹天气")
    parser.add_argument("--base_url", help="base_url", default="http://**************:1054/v1/")
    parser.add_argument("--model_name", help="model_name", default="qwen3_32b")
    parser.add_argument("--instruction", help="instruction", default="成都天气怎么样")
    parser.add_argument("--vlt_base_url", help="vlt_base_url", default="http://**************:1054/v1/")
    parser.add_argument("--vlt_model_name", help="vlt_model_name", default="ui-tars")
    parser.add_argument("--use_vlt", help="use_vlt", default="0")
    args = parser.parse_args()
    sys.stderr.write(f"参数: {args}\n")
    screenshots, answers, answers_raw_data, target_id, screens = run(
        args.app_id, args.app_name, args.base_url, args.model_name, args.instruction
    )
    screens = []
    label_indexs = []
    xpaths = []
    xmls = []
    result = {
        "screenshots": screenshots if screenshots else [],
        "answers": answers if answers else [],
        "screens": screens if screens else [],
        "xpaths": xpaths if xpaths else [],
        "label_indexs": label_indexs if label_indexs else [],
        "xmls": xmls if xmls else [],
        "target_id": target_id if target_id else "",
        "status": "success" if screenshots else "failed",
    }
    json_output = json.dumps(result, ensure_ascii=False)
    sys.stdout.write(json_output + "\n")
    sys.stdout.flush()
    sys.exit(0)
