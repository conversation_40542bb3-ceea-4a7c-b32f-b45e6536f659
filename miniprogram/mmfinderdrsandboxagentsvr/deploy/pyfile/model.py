"""定义脚本的输入输出类型"""

from enum import StrEnum
from typing import Protocol

from pydantic import BaseModel


class PythonInput(BaseModel):
    """脚本的输入类型"""

    app_id: str
    app_name: str
    base_url: str
    instruction: str
    model_name: str
    prompt: str
    prompt_vlt: str
    prompt_vlt_v1: str
    run_mode: str
    use_vlt: str
    use_wait_model: str
    vlt_base_url: str
    vlt_base_url_v1: str
    vlt_model_name: str
    vlt_model_name_v1: str
    wait_model_base_url: str
    wait_model_name: str


class PythonResultStatus(StrEnum):
    """脚本输出的参数 status 枚举"""

    FAILED = "failed"
    SUCCESS = "success"


class PythonResult(BaseModel):
    """脚本的输出类型"""

    # 每一步骤的沙箱操作，例如点击或者 scroll 或者 input 等
    answers: list[str]
    # 每一步骤的沙箱操作，例如点击或者 scroll 或者 input 等，是大模型输出的 raw data
    answers_raw_data: list[str]
    # 中断信息，为空则不是中断退出
    interrupt: dict
    # 文本模型才添加：每一点击步骤中的被点击元素在 xpaths 中的序号，和 xmls 一一对应
    label_indexs: list[str]
    # 文本模型才添加：每一步骤结果的文本输入，是 dom xml 的简化，和 xmls 一一对应
    screens: list[str]
    # 每一步骤结果的截图
    screenshots: list[str]
    # 脚本执行的状态
    status: PythonResultStatus
    # 小程序页面的 target_id
    target_id: str
    # 文本模型才添加：每一步骤结果的整体页面的 dom xml
    xmls: list[str]
    # 文本模型才添加：每一步骤结果的全部可以被点击的元素 xpath 路径列表，和 xmls 一一对应
    xpaths: list[str]


class TaskhandlerProtocol(Protocol):
    """handle_task 的函数定义"""

    def __call__(self, python_input: PythonInput) -> PythonResult: ...
