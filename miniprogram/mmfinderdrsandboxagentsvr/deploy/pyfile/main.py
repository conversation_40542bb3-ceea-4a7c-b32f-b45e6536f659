"""主入口"""

import argparse
import sys
from concurrent.futures import ThreadPoolExecutor

from model import PythonInput, PythonResult
from task_handler import get_task_handler

__DEFAULT_PROMPT = """ # 小程序自动化操作指令
## 任务说明
你是一个微信小程序自动化助手，需要根据用户的操作指令和当前页面结构，精准选择接下来要操作的元素并返回指定格式的结果。

## 输入内容
1. 小程序名称及当前页面结构（包含所有可点击元素的层级、文字描述和索引标记）
2. 用户的整体任务指令（中文自然语言描述）
3. 用户在当前页面之前的操作

## 输出要求
- **严格遵循**以下输出格式：
  `<label_index>[n]</label_index><search>输入文本内容</search>`
- 如果没有需要输入的文本内容，保持`<search></search>`为空
- 禁止添加任何解释、说明或其他文字
- 如果找不到匹配项，输出`<label_index>[-1]</label_index><search></search>`

## 处理规则
1. 分析用户指令，识别操作类型（点击/输入）
2. 在页面结构中寻找最匹配的可点击元素
3. 从指令中提取需要输入的文本内容（如果有）
4. 如果页面需要同意并继续优先选择此元素
5. 当指令同时包含点击和输入要求时：
   - 优先匹配可点击元素
   - 在`<search>`标签中填入需要输入的文本

## 示例1
用户指令：点击机票预订
输出：`<label_index>[4]</label_index><search></search>`

## 示例2
用户指令：在酒店预订搜索北京
输出：`<label_index>[2]</label_index><search>北京</search>`

## 当前页面结构
{}
##小程序名称: {}

用户的整体任务指令：{}
用户在当前页面之前的操作： {}
## 请选择用户在当前页面应该点击的元素
    """
__DEFAULT_PROMPT_VLT = """
你是一个小程序操作助手，上述图像是按顺序操作任务指令，最近一张是当前图像。
当前指令: 如果当前页面能够搜索优先使用搜索功能来实现指令，{}，请问在当前图像里我要怎么操作？当前只能有一种输出选项：只能回复接下来的一个动作，不能回复多个动作，并给出在页面的精确像素坐标（x y），最终输出格式严格遵循<think>xxx</think><answer>xxx</answer>这个格式。answer动作只能在以下6 类集合选择：1. click x y(代表点击像素坐标 x,y)，2. input x y t（代表在x y 选中输入框并输入文本t） 3. finish（代表任务成功结束） 4. stop（代表任务无法进行 进程终止）5. scroll x（代表下滑x个像素，x为负数代表上滑|x|个像素）6. wait t（代表当前页面处于加载中，可以等待t秒）。 思考内容：首先通过对话上下文判断上一步的操作是否生效，如果上一步操作无效，需要反思并采取正确的动作，例如 1.  input x y t 后页面是否有显示 input 的内容t，没有的话则操作未生效，考虑网页不支持本步input 而应该采取click x y 操作使用点击小程序拉起的键盘输入 2. click x y 后操作未生效考虑点击位置无效，应该采取点击其他位置click x1 y1实现指令 3. assistant重复输入相同命令表示当前操作无效，需要采取其他方法实现指令 4. 需要详细检查 input 的内容 t 是否正确显示在页面中 5. 最近两张图像没有变化时需要反思动作是否正确
"""
__DEFAULT_PROMPT_VLT_V1 = """
你是一个小程序操作助手，上述图像是按顺序操作任务指令，最近一张是当前图像。
当前指令: 如果当前页面能够搜索优先使用搜索功能来实现指令，{}，用户历史操作总结为:{}，请问在当前图像里我要怎么操作？当前只能有一种输出选项：只能回复接下来的一个动作，不能回复多个动作，并给出在页面的精确像素坐标（x y），最终输出格式严格遵循<think>xxx</think><answer>xxx</answer>这个格式。answer动作只能在以下6 类集合选择：1. click x y(代表点击像素坐标 x,y)，2. input x y t（代表在x y 选中输入框并输入文本t） 3. finish（代表任务成功结束） 4. stop（代表任务无法进行 进程终止）5. scroll x（代表下滑x个像素，x为负数代表上滑|x|个像素）6. wait t（代表当前页面处于加载中，可以等待t秒）。 思考内容：首先通过对话上下文判断上一步的操作是否生效，如果上一步操作无效，需要反思并采取正确的动作，例如 1.  input x y t 后页面是否有显示 input 的内容t，没有的话则操作未生效，考虑网页不支持本步input 而应该采取click x y 操作使用点击小程序拉起的键盘输入 2. click x y 后操作未生效考虑点击位置无效，应该采取点击其他位置click x1 y1实现指令 3. assistant重复输入>相同命令表示当前操作无效，需要采取其他方法实现指令 4. 需要详细检查 input 的内容 t 是否正确显示在页面中 5. 最近两张图像没有变化时需要反思动作是否正确
"""


def __execute_function_with_timeout(timeout: float, func, *arg) -> PythonResult:
    with ThreadPoolExecutor() as executor:
        future = executor.submit(func, *arg)
        return future.result(timeout=timeout)


def __get_python_input() -> PythonInput:
    parser = argparse.ArgumentParser(description="")
    parser.add_argument("--app_id", default="wx3dcca19d0aa51755")
    parser.add_argument("--app_name", default="星巴克")
    parser.add_argument("--base_url", default="http://drhttpsvr.polaris:8000/v1/llm_luban_minip_base4k_v20250424_01-0506-21/")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--instruction", default="帮我点一杯大杯美式咖啡")
    parser.add_argument("--model_name", default="llm_luban_minip_base4k_v20250424_01-0506-21")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--prompt", default=__DEFAULT_PROMPT)
    parser.add_argument("--prompt_vlt", default=__DEFAULT_PROMPT_VLT)
    parser.add_argument("--prompt_vlt_v1", default=__DEFAULT_PROMPT_VLT_V1)
    parser.add_argument("--run_mode", default="text_infer_v3")
    parser.add_argument("--use_vlt", default="1")
    parser.add_argument("--use_wait_model", default="1")
    parser.add_argument("--vlt_base_url", default="http://drhttpsvr.polaris:8000/v1/llm-luban-cpsmixrs_xiaode526_ck8100_export-0526-16")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--vlt_base_url_v1", default="http://*************:8000/v1/")
    parser.add_argument("--vlt_model_name", default="llm-luban-cpsmixrs_xiaode526_ck8100_export-0526-16")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--vlt_model_name_v1", default="eval_zhongpuwang-user_xiaodezhang_llm_luban_xiaochengxu_qwen25vl32b_v20250530_900_export-0530-11")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--wait_model_base_url", default="http://drhttpsvr.polaris:8000/v1/llm_luban_minip_waitmodelduphard_xiaode_ck200_export-0603-20/")  # fmt: skip # pylint: disable=C0301
    parser.add_argument("--wait_model_name", default="llm_luban_minip_waitmodelduphard_xiaode_ck200_export-0603-20")  # fmt: skip # pylint: disable=C0301
    args = parser.parse_args()
    return PythonInput(**args.__dict__)


if __name__ == "__main__":
    python_input = __get_python_input()
    if handler := get_task_handler(python_input.run_mode):
        res = __execute_function_with_timeout(120, handler, python_input)
        # 将输出通过 stdout 返回
        sys.stdout.write(res.model_dump_json() + "\n")
        sys.stdout.flush()
        # 确保后续不会有其他打印输出干扰
        sys.exit(0)
    else:
        raise ValueError(f"wrong run_mode {python_input.run_mode}")
