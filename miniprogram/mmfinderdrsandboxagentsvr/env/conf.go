// Package env 配置
package env

// PostgreSQLConfig 数据库配置
type PostgreSQLConfig struct {
	Database     string `toml:"database"`
	Host         string `toml:"host"`
	MaxIdleConns int    `toml:"max_idle_conns"`
	MaxOpenConns int    `toml:"max_open_conns"`
	Password     string `toml:"password"`
	Port         uint16 `toml:"port"`
	Username     string `toml:"username"`
}

// PulsarConfig Pulsar 配置
type PulsarConfig struct {
	AuthenticationToken string `toml:"authentication_token"`
	ServiceURL          string `toml:"service_url"`
	Topic               string `toml:"topic"`
}

// Config 每个配置作为成员变量放到一个对象里
type Config struct {
	PostgreSQL PostgreSQLConfig `toml:"postgresql"`
	Pulsar     PulsarConfig     `toml:"pulsar"`
}
