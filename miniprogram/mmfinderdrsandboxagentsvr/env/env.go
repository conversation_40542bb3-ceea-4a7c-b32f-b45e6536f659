// Package env 自动初始化
package env

import (
	"git.woa.com/wego/wxg/weenv"
	"github.com/BurntSushi/toml"
)

// Conf 每个配置作为成员变量放到一个对象里
var Conf = &Config{}

// IP 本机非回环地址
var IP string

func init() {
	initConf()
	initEnv()
}

func initConf() {
	confPath := "/home/<USER>/mmfinderdrsandboxagentsvr/etc/conf.toml"
	_, err := toml.DecodeFile(confPath, Conf)
	if err != nil {
		panic(err)
	}
}

func initEnv() {
	IP = weenv.GetInnerIp()
}
